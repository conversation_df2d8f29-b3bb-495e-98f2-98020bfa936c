/**
 * Discord Platform Interface
 * Provides Discord integration for Gentian Aphrodite
 * Independent implementation replacing fount platform dependencies
 */

import { Client, GatewayIntentBits, Events, ChannelType, ActivityType } from 'discord.js'
import { chatManager } from '../../core/chat-manager.mjs'
import { aiManager } from '../../core/ai-manager.mjs'
import { splitDiscordReply, getMessageFullContent } from './tools.mjs'
import { createDiscordApiPlugin, needsDiscordApi } from './api-plugin.mjs'
import { discordWorld, getDiscordBehaviorGuidelines } from './world.mjs'

/**
 * @typedef {import('../../types/index.mjs').DiscordConfig} DiscordConfig
 * @typedef {import('../../types/index.mjs').PlatformInterface} PlatformInterface
 * @typedef {import('../../types/index.mjs').ChatMessage} ChatMessage
 */

/**
 * Discord Platform implementation
 * @implements {PlatformInterface}
 */
export class DiscordPlatform {
	/**
	 * @param {DiscordConfig} config - Discord configuration
	 * @param {Object} app - Application instance
	 */
	constructor(config, app) {
		this.config = config
		this.app = app
		this.name = 'Discord'
		this.client = null
		this.isRunning = false
		this.userCache = new Map()
		this.channelCache = new Map()

		// Discord interface configuration
		this.interfaceConfig = {
			OwnerUserName: app.config?.character?.owner?.username || 'owner',
			OwnerDiscordID: app.config?.character?.owner?.discordId || '',
			OwnerNameKeywords: app.config?.character?.owner?.keywords || ['主人', 'owner'],
			BotActivityName: app.config?.character?.info?.name || '龙胆•阿芙萝黛蒂',
			BotActivityType: 'Watching'
		}
	}

	/**
	 * Start the Discord platform
	 * @returns {Promise<void>}
	 */
	async start() {
		if (this.isRunning) {
			console.log('⚠️ Discord platform is already running')
			return
		}

		console.log('🚀 Starting Discord platform...')

		try {
			// Validate configuration
			if (!this.config.token) {
				throw new Error('Discord bot token is required. Please set DISCORD_BOT_TOKEN in your .env file.')
			}

			// Initialize Discord client
			await this.initializeClient()

			// Connect to Discord
			await this.connect()

			console.log('✅ Discord platform started successfully')
			this.isRunning = true

		} catch (error) {
			console.error('❌ Failed to start Discord platform:', error)
			throw error
		}
	}

	/**
	 * Initialize Discord client with proper intents and configuration
	 * @private
	 * @returns {void}
	 */
	initializeClient() {
		console.log('🔧 Initializing Discord client...')

		// Create Discord client with required intents
		this.client = new Client({
			intents: [
				GatewayIntentBits.Guilds,
				GatewayIntentBits.GuildMessages,
				GatewayIntentBits.MessageContent,
				GatewayIntentBits.DirectMessages,
				GatewayIntentBits.GuildMembers,
				GatewayIntentBits.GuildPresences
			],
			partials: ['MESSAGE', 'CHANNEL', 'REACTION']
		})

		// Set up event handlers
		this.setupEventHandlers()

		console.log('✅ Discord client initialized')
	}

	/**
	 * Set up Discord event handlers
	 * @private
	 * @returns {void}
	 */
	setupEventHandlers() {
		// Ready event
		this.client.on(Events.ClientReady, () => {
			console.log(`🤖 Discord bot ready: ${this.client.user.tag}`)
			this.setupBotActivity()
		})

		// Message events
		this.client.on(Events.MessageCreate, async (message) => {
			await this.handleMessage(message)
		})

		this.client.on(Events.MessageUpdate, async (oldMessage, newMessage) => {
			await this.handleMessageUpdate(oldMessage, newMessage)
		})

		// Error handling
		this.client.on(Events.Error, (error) => {
			console.error('Discord client error:', error)
		})

		this.client.on(Events.Warn, (warning) => {
			console.warn('Discord client warning:', warning)
		})

		// Connection events
		this.client.on(Events.Disconnect, () => {
			console.log('🔌 Discord client disconnected')
		})

		this.client.on(Events.Reconnecting, () => {
			console.log('🔄 Discord client reconnecting...')
		})
	}

	/**
	 * Set up bot activity status
	 * @private
	 * @returns {void}
	 */
	setupBotActivity() {
		if (!this.client.user) return

		try {
			this.client.user.setActivity(this.interfaceConfig.BotActivityName, {
				type: ActivityType[this.interfaceConfig.BotActivityType] || ActivityType.Watching
			})
			console.log(`🎭 Bot activity set: ${this.interfaceConfig.BotActivityType} ${this.interfaceConfig.BotActivityName}`)
		} catch (error) {
			console.error('Failed to set bot activity:', error)
		}
	}

	/**
	 * Connect to Discord
	 * @private
	 * @returns {Promise<void>}
	 */
	connect() {
		console.log('🔌 Connecting to Discord...')

		return new Promise((resolve, reject) => {
			const timeout = setTimeout(() => {
				reject(new Error('Discord connection timeout'))
			}, 30000) // 30 second timeout

			this.client.once(Events.ClientReady, () => {
				clearTimeout(timeout)
				console.log(`✅ Connected to Discord as ${this.client.user.tag}`)
				resolve()
			})

			this.client.once(Events.Error, (error) => {
				clearTimeout(timeout)
				reject(error)
			})

			this.client.login(this.config.token).catch(reject)
		})
	}

	/**
	 * Handle incoming Discord message
	 * @private
	 * @param {import('discord.js').Message} message - Discord message
	 * @returns {Promise<void>}
	 */
	async handleMessage(message) {
		try {
			// Skip bot messages and system messages
			if (message.author.bot || message.system) return

			// Convert Discord message to internal format
			const chatMessage = await this.convertDiscordMessage(message)
			if (!chatMessage) return

			// Add to chat history
			await chatManager.addMessage(message.channel.id, chatMessage)

			// Check if bot should respond
			const shouldRespond = this.shouldRespondToMessage(message, chatMessage)
			if (!shouldRespond) return

			// Send typing indicator
			await this.sendTyping(message.channel.id)

			// Generate AI response
			const response = await this.generateResponse(message.channel.id, chatMessage)
			if (response) {
				await this.sendMessage(message.channel.id, response)
			}

		} catch (error) {
			console.error('Error handling Discord message:', error)
		}
	}

	/**
	 * Handle Discord message update
	 * @private
	 * @param {import('discord.js').Message} oldMessage - Old message
	 * @param {import('discord.js').Message} newMessage - New message
	 * @returns {Promise<void>}
	 */
	async handleMessageUpdate(oldMessage, newMessage) {
		try {
			// Skip bot messages and system messages
			if (newMessage.author.bot || newMessage.system) return

			// Convert updated message to internal format
			const chatMessage = await this.convertDiscordMessage(newMessage)
			if (!chatMessage) return

			// Update in chat history
			await chatManager.updateMessage(newMessage.channel.id, newMessage.id, chatMessage)

		} catch (error) {
			console.error('Error handling Discord message update:', error)
		}
	}

	/**
	 * Convert Discord message to internal ChatMessage format
	 * @private
	 * @param {import('discord.js').Message} message - Discord message
	 * @returns {Promise<ChatMessage|null>} Converted message or null if invalid
	 */
	async convertDiscordMessage(message) {
		try {
			// Cache user information
			this.userCache.set(message.author.id, {
				id: message.author.id,
				username: message.author.username,
				displayName: message.author.displayName || message.author.username,
				avatar: message.author.displayAvatarURL()
			})

			// Determine if message is from owner
			const isFromOwner = this.isOwnerMessage(message)

			// Process message content using enhanced tools
			let content = await getMessageFullContent(message, this.client)

			// Handle attachments
			const files = []
			for (const attachment of message.attachments.values()) {
				files.push({
					name: attachment.name,
					url: attachment.url,
					size: attachment.size,
					contentType: attachment.contentType
				})
			}

			// Handle embeds
			if (message.embeds.length > 0) {
				for (const embed of message.embeds) {
					if (embed.description) {
						content += `\n[Embed] ${embed.description}`
					}
					if (embed.title) {
						content += `\n[Embed Title] ${embed.title}`
					}
				}
			}

			return {
				id: message.id,
				content: content.trim(),
				role: isFromOwner ? 'user' : 'char',
				name: message.author.displayName || message.author.username,
				timestamp: message.createdTimestamp,
				files,
				extension: {
					platform: 'discord',
					platform_message_id: message.id,
					platform_channel_id: message.channel.id,
					platform_user_id: message.author.id,
					platform_guild_id: message.guild?.id,
					is_direct_message: message.channel.type === ChannelType.DM,
					is_from_owner: isFromOwner,
					mentions_bot: message.mentions.users.has(this.client.user.id),
					mentions_owner: this.mentionsOwner(message),
					discord_message_obj: message
				}
			}

		} catch (error) {
			console.error('Error converting Discord message:', error)
			return null
		}
	}

	/**
	 * Check if message is from owner
	 * @private
	 * @param {import('discord.js').Message} message - Discord message
	 * @returns {boolean} True if from owner
	 */
	isOwnerMessage(message) {
		return message.author.username === this.interfaceConfig.OwnerUserName ||
			   message.author.id === this.interfaceConfig.OwnerDiscordID ||
			   this.interfaceConfig.OwnerNameKeywords.some(keyword =>
				   message.author.username.toLowerCase().includes(keyword.toLowerCase())
			   )
	}

	/**
	 * Check if message mentions owner
	 * @private
	 * @param {import('discord.js').Message} message - Discord message
	 * @returns {boolean} True if mentions owner
	 */
	mentionsOwner(message) {
		return message.mentions.users.some(user =>
			user.username === this.interfaceConfig.OwnerUserName ||
			user.id === this.interfaceConfig.OwnerDiscordID
		)
	}

	/**
	 * Process mentions in message content
	 * @private
	 * @param {string} content - Message content
	 * @param {import('discord.js').Message} message - Discord message
	 * @returns {string} Processed content
	 */
	processMentions(content, message) {
		// Replace user mentions with usernames
		for (const [_, user] of message.mentions.users) {
			const mentionTag = `<@${user.id}>`
			const mentionTagNick = `<@!${user.id}>`
			content = content.replace(new RegExp(mentionTag, 'g'), `@${user.username}`)
			content = content.replace(new RegExp(mentionTagNick, 'g'), `@${user.username}`)
		}

		// Replace channel mentions
		for (const [_, channel] of message.mentions.channels) {
			const mentionTag = `<#${channel.id}>`
			content = content.replace(new RegExp(mentionTag, 'g'), `#${channel.name}`)
		}

		// Replace role mentions
		for (const [_, role] of message.mentions.roles) {
			const mentionTag = `<@&${role.id}>`
			content = content.replace(new RegExp(mentionTag, 'g'), `@${role.name}`)
		}

		return content
	}

	/**
	 * Check if bot should respond to message
	 * @private
	 * @param {import('discord.js').Message} message - Discord message
	 * @param {ChatMessage} chatMessage - Converted chat message
	 * @returns {boolean} True if should respond
	 */
	shouldRespondToMessage(message, chatMessage) {
		// Always respond to DMs
		if (message.channel.type === ChannelType.DM) {
			return true
		}

		// Respond if bot is mentioned
		if (chatMessage.extension.mentions_bot) {
			return true
		}

		// Respond if owner is mentioned and message is from owner
		if (chatMessage.extension.is_from_owner && chatMessage.extension.mentions_owner) {
			return true
		}

		// Random chance to respond (configurable)
		const randomChance = 0.1 // 10% chance
		return Math.random() < randomChance
	}

	/**
	 * Generate AI response for message
	 * @private
	 * @param {string} channelId - Channel ID
	 * @param {ChatMessage} message - Trigger message
	 * @returns {Promise<Object|null>} Response object or null
	 */
	async generateResponse(channelId, message) {
		try {
			// Get chat history
			const history = await chatManager.getHistory(channelId, 20)

			// Build prompt
			const prompt = this.buildPrompt(history, message)

			// Call AI
			const response = await aiManager.call('chat', prompt)

			if (response && response.content) {
				return {
					content: response.content,
					files: [],
					extension: {
						source: response.source,
						platform: 'discord'
					}
				}
			}

			return null

		} catch (error) {
			console.error('Error generating AI response:', error)
			return null
		}
	}

	/**
	 * Build prompt for AI with Discord context
	 * @private
	 * @param {ChatMessage[]} history - Chat history
	 * @param {ChatMessage} triggerMessage - Trigger message
	 * @returns {string} AI prompt
	 */
	buildPrompt(history, triggerMessage) {
		const config = this.app.config
		const characterName = config.character.info.name
		const characterDescription = config.character.info.description || ''

		// Get Discord world guidelines
		const worldPrompt = discordWorld.interfaces.chat.GetPrompt()
		const discordGuidelines = worldPrompt.text[0].content

		// Get channel-specific behavior guidelines
		const isDirectMessage = triggerMessage.extension?.is_direct_message || false
		const channelType = triggerMessage.extension?.discord_message_obj?.channel?.type || 'text'
		const behaviorGuidelines = getDiscordBehaviorGuidelines(channelType, isDirectMessage)

		let prompt = `你是 ${characterName}。${characterDescription}\n\n`
		prompt += `${discordGuidelines}\n\n`
		prompt += `${behaviorGuidelines}\n\n`
		prompt += '聊天历史:\n'

		// Add recent history
		for (const msg of history.slice(-10)) {
			const role = msg.role === 'user' ? msg.name : characterName
			prompt += `${role}: ${msg.content}\n`
		}

		// Add context about Discord features if needed
		if (needsDiscordApi(triggerMessage.content)) {
			prompt += '\n注意：用户的请求可能涉及 Discord 功能，你可以参考 Discord API 插件的功能。'
		}

		prompt += `\n请以 ${characterName} 的身份回复最后一条消息。保持角色一致性，回复要自然、有趣，符合 Discord 聊天环境。`

		return prompt
	}

	/**
	 * Send message (Discord implementation)
	 * @param {string} channelId - Discord channel ID
	 * @param {Object} reply - Reply object
	 * @returns {Promise<void>}
	 */
	async sendMessage(channelId, reply) {
		try {
			const channel = await this.client.channels.fetch(channelId)
			if (!channel) {
				throw new Error(`Channel ${channelId} not found`)
			}

			// Split long messages using Discord-specific logic
			const messages = splitDiscordReply(reply.content || '', 2000)

			for (const messageContent of messages) {
				if (messageContent.trim()) {
					const sentMessage = await channel.send({
						content: messageContent,
						files: reply.files || []
					})

					// Add sent message to chat history
					const chatMessage = this.convertDiscordMessage(sentMessage)
					if (chatMessage) {
						await chatManager.addMessage(channelId, chatMessage)
					}
				}
			}

		} catch (error) {
			console.error('Error sending Discord message:', error)
			throw error
		}
	}

	/**
	 * Send typing indicator (Discord implementation)
	 * @param {string} channelId - Discord channel ID
	 * @returns {Promise<void>}
	 */
	async sendTyping(channelId) {
		try {
			const channel = await this.client.channels.fetch(channelId)
			if (channel && channel.sendTyping) {
				await channel.sendTyping()
			}
		} catch (error) {
			console.error('Error sending typing indicator:', error)
		}
	}

	/**
	 * Get chat history (Discord implementation)
	 * @param {string} channelId - Discord channel ID
	 * @param {number} limit - Message limit
	 * @returns {Promise<ChatMessage[]>} Chat history
	 */
	async getHistory(channelId, limit) {
		try {
			// First try to get from chat manager
			const history = await chatManager.getHistory(channelId, limit)
			if (history && history.length > 0) {
				return history
			}

			// If no history in chat manager, fetch from Discord
			const channel = await this.client.channels.fetch(channelId)
			if (!channel || !channel.messages) {
				return []
			}

			const messages = await channel.messages.fetch({ limit })
			const chatMessages = []

			for (const [_, message] of messages) {
				const chatMessage = this.convertDiscordMessage(message)
				if (chatMessage) {
					chatMessages.push(chatMessage)
				}
			}

			// Reverse to get chronological order
			chatMessages.reverse()

			// Add to chat manager for future use
			for (const chatMessage of chatMessages) {
				await chatManager.addMessage(channelId, chatMessage)
			}

			return chatMessages

		} catch (error) {
			console.error('Error getting Discord history:', error)
			return []
		}
	}



	/**
	 * Get bot ID (Discord implementation)
	 * @returns {string} Bot ID
	 */
	getBotId() {
		return this.client?.user?.id || 'unknown'
	}

	/**
	 * Get bot name (Discord implementation)
	 * @returns {string} Bot name
	 */
	getBotName() {
		return this.client?.user?.username || this.app.config.character.info.name
	}

	/**
	 * Cleanup platform (Discord implementation)
	 * @returns {Promise<void>}
	 */
	async destroy() {
		if (this.client) {
			await this.client.destroy()
		}
		this.isRunning = false
		console.log('🔌 Discord Platform shutdown')
	}
}
