/**
 * AI Source management - Independent implementation
 * Replaces fount platform dependencies with direct AI manager integration
 */

import { aiManager } from '../src/core/ai-manager.mjs'
import { configManager } from '../src/core/config.mjs'

/**
 * @typedef {import('../src/types/index.mjs').AISource} AISource
 */

/**
 * Legacy AI sources mapping for compatibility
 * @type {Record<string, string>}
 */
export let AIsources = {
	'detail-thinking': '',
	'web-browse': '',
	nsfw: '',
	sfw: '',
	expert: '',
	logic: ''
}

/**
 * Get AI source data for legacy compatibility
 * @returns {Record<string, string>} AI source mapping
 */
export function getAISourceData() {
	const config = configManager.get()
	if (config?.character?.aiSources) {
		return config.character.aiSources
	}
	return AIsources
}

/**
 * Set AI source data for legacy compatibility
 * @param {Record<string, string>} data - AI source mapping
 * @returns {Promise<void>}
 */
export async function setAISourceData(data) {
	// Update the legacy mapping
	AIsources = { ...AIsources, ...data }

	// Update configuration
	const config = configManager.get()
	if (config) {
		config.character.aiSources = AIsources
		await configManager.save({ character: config.character })
	}

	// Reload AI manager to pick up new sources
	await aiManager.reload()
}

/**
 * @param {string} name
 * @returns {string[]}
 */
export function GetAISourceCallingOrder(name) {
	// 对于不同任务需求，按照指定顺序尝试调用AI
	switch (name) {
		case 'detail-thinking':
			// 我们假设用户给龙胆设置的AI来源中，来源的智商顺序以以下顺序排列：
			// 详细思考模型，专家模型，正经使用模型，网页浏览模型，色情模型，简易逻辑模型
			// 在详细思考任务中，我们以此顺序回落AI来源
			return ['detail-thinking', 'expert', 'sfw', 'web-browse', 'nsfw', 'logic']
		case 'web-browse':
			// 在网页浏览任务中，我们优先调用网页浏览模型，再以智商顺序回落AI来源
			return ['web-browse', 'detail-thinking', 'expert', 'sfw', 'nsfw', 'logic']
		case 'expert':
			// 在专家任务中，我们优先调用专家模型，再以智商顺序回落AI来源
			return ['expert', 'detail-thinking', 'sfw', 'web-browse', 'nsfw', 'logic']
		case 'sfw':
			// 在普通但非色情任务中，我们在正经使用模型回落时优先使用专家模型或详细思考模型以获得最好的结果，之后按智商顺序回落
			return ['sfw', 'expert', 'detail-thinking', 'web-browse', 'nsfw', 'logic']
		case 'nsfw':
			// 在色情任务中，我们假设正经使用模型或专家模型难以产出优质文本，而逻辑模型则是次优解
			return ['nsfw', 'logic', 'web-browse', 'sfw', 'expert', 'detail-thinking']
		case 'logic':
			// 在逻辑判断中，我们使用智商顺序的倒序来回落调用，以最大程度减少不必要的算力损耗
			return ['logic', 'nsfw', 'web-browse', 'sfw', 'expert', 'detail-thinking']
	}
}

/**
 * Check if any AI sources are available
 * @returns {boolean} True if no AI sources are available
 */
export function noAISourceAvailable() {
	const hasAvailable = aiManager.hasAvailableSources()
	if (!hasAvailable) {
		console.error('No AI source available')
	}
	return !hasAvailable
}

/**
 * Last used AI source name for legacy compatibility
 * @type {string|null}
 */
export let last_used_AIsource = null

/**
 * Ordered AI source calling with fallback
 * @param {string} name - Task type name
 * @param {(prompt: string) => Promise<string>} caller - AI caller function
 * @param {number} trytimes - Number of retry attempts
 * @param {(err: Error) => Promise<void>} error_logger - Error logger function
 * @returns {Promise<{content: string; files: any[]}>} AI response
 */
export async function OrderedAISourceCalling(name, caller, trytimes = 3, error_logger = console.error) {
	try {
		// Use the new AI manager for calling
		const result = await aiManager.call(name, '', {}, trytimes)
		last_used_AIsource = result.source

		// For legacy compatibility, call the provided caller function with the prompt
		// This allows existing code to work with minimal changes
		let finalContent = result.content

		// If caller is provided and expects a different interface, adapt it
		if (caller && typeof caller === 'function') {
			try {
				// Create a mock source object for legacy compatibility
				const mockSource = {
					name: result.source,
					call: async (prompt) => {
						const aiResult = await aiManager.call(name, prompt, {}, trytimes)
						return aiResult.content
					}
				}

				// Call the legacy caller function
				const callerResult = await caller(mockSource)
				if (typeof callerResult === 'string') {
					finalContent = callerResult
				}
			} catch (callerError) {
				await error_logger(callerError)
				// Fall back to AI manager result
			}
		}

		return {
			content: finalContent,
			files: [] // Legacy compatibility - no files for now
		}
	} catch (error) {
		await error_logger(error)
		throw error
	}
}
