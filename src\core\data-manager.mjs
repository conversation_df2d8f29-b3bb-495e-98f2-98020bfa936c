/**
 * Data persistence manager
 * Independent implementation replacing fount platform dependencies
 */

import { ensureDir } from 'https://deno.land/std@0.208.0/fs/mod.ts'
import { dirname } from 'https://deno.land/std@0.208.0/path/mod.ts'

/**
 * Data manager for handling file-based persistence
 */
export class DataManager {
	/**
	 * Save data to JSON file
	 * @param {string} filepath - File path relative to project root
	 * @param {any} data - Data to save
	 * @returns {Promise<void>}
	 */
	static async save(filepath, data) {
		try {
			// Ensure directory exists
			const dir = dirname(filepath)
			await ensureDir(dir)

			// Convert data to JSON string
			const jsonString = JSON.stringify(data, null, 2)

			// Write to file
			await Deno.writeTextFile(filepath, jsonString)
		} catch (error) {
			console.error(`Failed to save data to ${filepath}:`, error)
			throw error
		}
	}

	/**
	 * Load data from JSON file
	 * @param {string} filepath - File path relative to project root
	 * @param {any} defaultValue - Default value if file doesn't exist
	 * @returns {Promise<any>} Loaded data
	 */
	static async load(filepath, defaultValue = null) {
		try {
			const content = await Deno.readTextFile(filepath)
			return JSON.parse(content)
		} catch (error) {
			if (error instanceof Deno.errors.NotFound) {
				// File doesn't exist, return default value
				return defaultValue
			}
			console.error(`Failed to load data from ${filepath}:`, error)
			throw error
		}
	}

	/**
	 * Check if file exists
	 * @param {string} filepath - File path to check
	 * @returns {Promise<boolean>} True if file exists
	 */
	static async exists(filepath) {
		try {
			await Deno.stat(filepath)
			return true
		} catch (error) {
			if (error instanceof Deno.errors.NotFound) {
				return false
			}
			throw error
		}
	}

	/**
	 * Delete file
	 * @param {string} filepath - File path to delete
	 * @returns {Promise<void>}
	 */
	static async delete(filepath) {
		try {
			await Deno.remove(filepath)
		} catch (error) {
			if (!(error instanceof Deno.errors.NotFound)) {
				console.error(`Failed to delete file ${filepath}:`, error)
				throw error
			}
		}
	}

	/**
	 * List files in directory
	 * @param {string} dirpath - Directory path
	 * @param {string} [extension] - File extension filter (e.g., '.json')
	 * @returns {Promise<string[]>} Array of file paths
	 */
	static async listFiles(dirpath, extension = null) {
		try {
			const files = []
			for await (const entry of Deno.readDir(dirpath)) {
				if (entry.isFile) {
					if (!extension || entry.name.endsWith(extension)) {
						files.push(`${dirpath}/${entry.name}`)
					}
				}
			}
			return files
		} catch (error) {
			if (error instanceof Deno.errors.NotFound) {
				return []
			}
			console.error(`Failed to list files in ${dirpath}:`, error)
			throw error
		}
	}

	/**
	 * Create backup of file
	 * @param {string} filepath - Original file path
	 * @param {string} [suffix] - Backup suffix (default: timestamp)
	 * @returns {Promise<string>} Backup file path
	 */
	static async backup(filepath, suffix = null) {
		if (!await this.exists(filepath)) {
			throw new Error(`File ${filepath} does not exist`)
		}

		const backupSuffix = suffix || new Date().toISOString().replace(/[:.]/g, '-')
		const backupPath = `${filepath}.backup.${backupSuffix}`

		await Deno.copyFile(filepath, backupPath)
		return backupPath
	}

	/**
	 * Restore file from backup
	 * @param {string} backupPath - Backup file path
	 * @param {string} originalPath - Original file path
	 * @returns {Promise<void>}
	 */
	static async restore(backupPath, originalPath) {
		if (!await this.exists(backupPath)) {
			throw new Error(`Backup file ${backupPath} does not exist`)
		}

		await Deno.copyFile(backupPath, originalPath)
	}

	/**
	 * Get file size
	 * @param {string} filepath - File path
	 * @returns {Promise<number>} File size in bytes
	 */
	static async getSize(filepath) {
		try {
			const stat = await Deno.stat(filepath)
			return stat.size
		} catch (error) {
			if (error instanceof Deno.errors.NotFound) {
				return 0
			}
			throw error
		}
	}

	/**
	 * Get file modification time
	 * @param {string} filepath - File path
	 * @returns {Promise<Date|null>} Modification time
	 */
	static async getModTime(filepath) {
		try {
			const stat = await Deno.stat(filepath)
			return stat.mtime
		} catch (error) {
			if (error instanceof Deno.errors.NotFound) {
				return null
			}
			throw error
		}
	}

	/**
	 * Clean up old files in directory
	 * @param {string} dirpath - Directory path
	 * @param {number} maxAge - Maximum age in milliseconds
	 * @param {string} [pattern] - File name pattern (regex)
	 * @returns {Promise<number>} Number of files deleted
	 */
	static async cleanup(dirpath, maxAge, pattern = null) {
		try {
			let deletedCount = 0
			const now = Date.now()
			const regex = pattern ? new RegExp(pattern) : null

			for await (const entry of Deno.readDir(dirpath)) {
				if (entry.isFile) {
					if (regex && !regex.test(entry.name)) {
						continue
					}

					const filepath = `${dirpath}/${entry.name}`
					const stat = await Deno.stat(filepath)
					
					if (stat.mtime && (now - stat.mtime.getTime()) > maxAge) {
						await Deno.remove(filepath)
						deletedCount++
					}
				}
			}

			return deletedCount
		} catch (error) {
			if (error instanceof Deno.errors.NotFound) {
				return 0
			}
			console.error(`Failed to cleanup directory ${dirpath}:`, error)
			throw error
		}
	}

	/**
	 * Ensure directory structure exists
	 * @param {string} dirpath - Directory path
	 * @returns {Promise<void>}
	 */
	static async ensureDir(dirpath) {
		await ensureDir(dirpath)
	}

	/**
	 * Get directory size (recursive)
	 * @param {string} dirpath - Directory path
	 * @returns {Promise<number>} Total size in bytes
	 */
	static async getDirSize(dirpath) {
		let totalSize = 0

		try {
			for await (const entry of Deno.readDir(dirpath)) {
				const entryPath = `${dirpath}/${entry.name}`
				
				if (entry.isFile) {
					const stat = await Deno.stat(entryPath)
					totalSize += stat.size
				} else if (entry.isDirectory) {
					totalSize += await this.getDirSize(entryPath)
				}
			}
		} catch (error) {
			if (!(error instanceof Deno.errors.NotFound)) {
				console.error(`Failed to get directory size for ${dirpath}:`, error)
				throw error
			}
		}

		return totalSize
	}

	/**
	 * Copy file with error handling
	 * @param {string} src - Source file path
	 * @param {string} dest - Destination file path
	 * @returns {Promise<void>}
	 */
	static async copy(src, dest) {
		try {
			// Ensure destination directory exists
			const destDir = dirname(dest)
			await ensureDir(destDir)

			await Deno.copyFile(src, dest)
		} catch (error) {
			console.error(`Failed to copy file from ${src} to ${dest}:`, error)
			throw error
		}
	}

	/**
	 * Move file with error handling
	 * @param {string} src - Source file path
	 * @param {string} dest - Destination file path
	 * @returns {Promise<void>}
	 */
	static async move(src, dest) {
		try {
			// Ensure destination directory exists
			const destDir = dirname(dest)
			await ensureDir(destDir)

			await Deno.rename(src, dest)
		} catch (error) {
			console.error(`Failed to move file from ${src} to ${dest}:`, error)
			throw error
		}
	}

	/**
	 * Read file as text with encoding support
	 * @param {string} filepath - File path
	 * @param {string} [encoding] - Text encoding (default: utf-8)
	 * @returns {Promise<string>} File content
	 */
	static async readText(filepath, encoding = 'utf-8') {
		try {
			return await Deno.readTextFile(filepath)
		} catch (error) {
			console.error(`Failed to read text file ${filepath}:`, error)
			throw error
		}
	}

	/**
	 * Write text to file with encoding support
	 * @param {string} filepath - File path
	 * @param {string} content - Text content
	 * @param {string} [encoding] - Text encoding (default: utf-8)
	 * @returns {Promise<void>}
	 */
	static async writeText(filepath, content, encoding = 'utf-8') {
		try {
			// Ensure directory exists
			const dir = dirname(filepath)
			await ensureDir(dir)

			await Deno.writeTextFile(filepath, content)
		} catch (error) {
			console.error(`Failed to write text file ${filepath}:`, error)
			throw error
		}
	}

	/**
	 * Read binary file
	 * @param {string} filepath - File path
	 * @returns {Promise<Uint8Array>} File content
	 */
	static async readBinary(filepath) {
		try {
			return await Deno.readFile(filepath)
		} catch (error) {
			console.error(`Failed to read binary file ${filepath}:`, error)
			throw error
		}
	}

	/**
	 * Write binary file
	 * @param {string} filepath - File path
	 * @param {Uint8Array} data - Binary data
	 * @returns {Promise<void>}
	 */
	static async writeBinary(filepath, data) {
		try {
			// Ensure directory exists
			const dir = dirname(filepath)
			await ensureDir(dir)

			await Deno.writeFile(filepath, data)
		} catch (error) {
			console.error(`Failed to write binary file ${filepath}:`, error)
			throw error
		}
	}
}
