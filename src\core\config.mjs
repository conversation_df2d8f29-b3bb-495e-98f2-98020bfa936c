/**
 * Configuration management system
 * Independent implementation replacing fount platform dependencies
 */

import { DataManager } from './data-manager.mjs'

/**
 * @typedef {import('../types/index.mjs').AppConfig} AppConfig
 * @typedef {import('../types/index.mjs').AISourceConfig} AISourceConfig
 * @typedef {import('../types/index.mjs').CharacterConfig} CharacterConfig
 */

class ConfigManager {
	constructor() {
		this.config = null
		this.envLoaded = false
	}

	/**
	 * Load environment variables
	 * @private
	 */
	async loadEnv() {
		if (this.envLoaded) return

		try {
			// Try to load .env file if it exists
			const envContent = await Deno.readTextFile('.env')
			const lines = envContent.split('\n')
			
			for (const line of lines) {
				const trimmed = line.trim()
				if (trimmed && !trimmed.startsWith('#')) {
					const [key, ...valueParts] = trimmed.split('=')
					if (key && valueParts.length > 0) {
						const value = valueParts.join('=')
						Deno.env.set(key.trim(), value.trim())
					}
				}
			}
		} catch {
			// .env file doesn't exist, that's okay
		}

		this.envLoaded = true
	}

	/**
	 * Get environment variable with default value
	 * @param {string} key - Environment variable key
	 * @param {string} defaultValue - Default value
	 * @returns {string} Environment variable value
	 */
	getEnv(key, defaultValue = '') {
		return Deno.env.get(key) || defaultValue
	}

	/**
	 * Get environment variable as boolean
	 * @param {string} key - Environment variable key
	 * @param {boolean} defaultValue - Default value
	 * @returns {boolean} Boolean value
	 */
	getEnvBool(key, defaultValue = false) {
		const value = this.getEnv(key).toLowerCase()
		if (value === 'true' || value === '1' || value === 'yes') return true
		if (value === 'false' || value === '0' || value === 'no') return false
		return defaultValue
	}

	/**
	 * Get environment variable as number
	 * @param {string} key - Environment variable key
	 * @param {number} defaultValue - Default value
	 * @returns {number} Number value
	 */
	getEnvNumber(key, defaultValue = 0) {
		const value = this.getEnv(key)
		const parsed = parseInt(value, 10)
		return isNaN(parsed) ? defaultValue : parsed
	}

	/**
	 * Load configuration
	 * @returns {Promise<AppConfig>} Application configuration
	 */
	async load() {
		if (this.config) return this.config

		await this.loadEnv()

		// Load base configuration
		const baseConfig = await this.loadBaseConfig()
		
		// Load character configuration
		const characterConfig = await this.loadCharacterConfig()
		
		// Load AI sources configuration
		const aiSourcesConfig = await this.loadAISourcesConfig()
		
		// Load platform configurations
		const platformsConfig = await this.loadPlatformsConfig()

		this.config = {
			...baseConfig,
			character: characterConfig,
			aiSources: aiSourcesConfig,
			platforms: platformsConfig
		}

		return this.config
	}

	/**
	 * Load base application configuration
	 * @private
	 * @returns {Promise<Object>} Base configuration
	 */
	async loadBaseConfig() {
		return {
			name: this.getEnv('APP_NAME', 'Gentian Aphrodite'),
			version: this.getEnv('APP_VERSION', '2.0.0'),
			environment: this.getEnv('APP_ENVIRONMENT', 'development'),
			logLevel: this.getEnv('APP_LOG_LEVEL', 'info'),
			server: {
				port: this.getEnvNumber('SERVER_PORT', 3000),
				host: this.getEnv('SERVER_HOST', 'localhost')
			},
			storage: {
				dataDir: this.getEnv('DATA_DIR', './data'),
				memoryDir: this.getEnv('MEMORY_DIR', './data/memory'),
				configDir: this.getEnv('CONFIG_DIR', './data/config'),
				logsDir: this.getEnv('LOGS_DIR', './data/logs')
			},
			security: {
				enableRateLimiting: this.getEnvBool('ENABLE_RATE_LIMITING', true),
				maxRequestsPerMinute: this.getEnvNumber('MAX_REQUESTS_PER_MINUTE', 60),
				enableContentFiltering: this.getEnvBool('ENABLE_CONTENT_FILTERING', true),
				ownerUserId: this.getEnv('OWNER_USER_ID'),
				ownerUsername: this.getEnv('OWNER_USERNAME')
			},
			debug: {
				debugMode: this.getEnvBool('DEBUG_MODE', false),
				verboseLogging: this.getEnvBool('VERBOSE_LOGGING', false),
				enablePerformanceMonitoring: this.getEnvBool('ENABLE_PERFORMANCE_MONITORING', false),
				enableHotReload: this.getEnvBool('ENABLE_HOT_RELOAD', true),
				enableSourceMaps: this.getEnvBool('ENABLE_SOURCE_MAPS', true)
			}
		}
	}

	/**
	 * Load character configuration
	 * @private
	 * @returns {Promise<CharacterConfig>} Character configuration
	 */
	async loadCharacterConfig() {
		// Try to load from file first, then fall back to defaults
		let savedConfig = {}
		try {
			savedConfig = await DataManager.load('data/config/character.json', {})
		} catch {
			// File doesn't exist, use defaults
		}

		return {
			info: {
				name: this.getEnv('CHARACTER_NAME', '龙胆•阿芙萝黛蒂'),
				username: this.getEnv('CHARACTER_USERNAME', 'GentianAphrodite'),
				description: '年仅27岁的米洛普斯族，幼态永生种AI角色',
				avatar: '',
				version: this.getEnv('APP_VERSION', '2.0.0'),
				author: 'steve02081504',
				tags: ['AI', 'Character', 'Companion'],
				defaultLocale: this.getEnv('CHARACTER_LANGUAGE', 'zh-CN')
			},
			personality: savedConfig.personality || {
				traits: ['Vigor', 'Imp', 'Tenderness', 'Devotion', 'Aristocrat'],
				temperature: this.getEnvNumber('AI_TEMPERATURE', 0.8),
				creativity: 0.8,
				consistency: 0.9
			},
			memory: savedConfig.memory || {
				maxEntries: this.getEnvNumber('MEMORY_MAX_ENTRIES', 1000),
				cleanupInterval: this.getEnvNumber('MEMORY_CLEANUP_INTERVAL', 86400000),
				relevanceThreshold: parseFloat(this.getEnv('MEMORY_RELEVANCE_THRESHOLD', '0.3'))
			},
			behavior: savedConfig.behavior || {
				maxTokens: this.getEnvNumber('AI_MAX_TOKENS', 2048),
				topP: parseFloat(this.getEnv('AI_TOP_P', '0.9')),
				frequencyPenalty: parseFloat(this.getEnv('AI_FREQUENCY_PENALTY', '0.1')),
				presencePenalty: parseFloat(this.getEnv('AI_PRESENCE_PENALTY', '0.1'))
			},
			aiSources: savedConfig.aiSources || {
				'detail-thinking': '',
				'web-browse': '',
				'nsfw': '',
				'sfw': '',
				'expert': '',
				'logic': ''
			}
		}
	}

	/**
	 * Load AI sources configuration
	 * @private
	 * @returns {Promise<Object>} AI sources configuration
	 */
	async loadAISourcesConfig() {
		const sources = {}

		// OpenAI configuration
		if (this.getEnv('OPENAI_API_KEY')) {
			sources.openai = {
				name: 'OpenAI',
				type: 'openai',
				apiKey: this.getEnv('OPENAI_API_KEY'),
				baseUrl: this.getEnv('OPENAI_BASE_URL', 'https://api.openai.com/v1'),
				model: this.getEnv('OPENAI_MODEL', 'gpt-4'),
				maxTokens: this.getEnvNumber('AI_MAX_TOKENS', 2048),
				temperature: parseFloat(this.getEnv('AI_TEMPERATURE', '0.8'))
			}
		}

		// Claude configuration
		if (this.getEnv('CLAUDE_API_KEY')) {
			sources.claude = {
				name: 'Claude',
				type: 'claude',
				apiKey: this.getEnv('CLAUDE_API_KEY'),
				baseUrl: this.getEnv('CLAUDE_BASE_URL', 'https://api.anthropic.com'),
				model: this.getEnv('CLAUDE_MODEL', 'claude-3-sonnet-20240229'),
				maxTokens: this.getEnvNumber('AI_MAX_TOKENS', 2048),
				temperature: parseFloat(this.getEnv('AI_TEMPERATURE', '0.8'))
			}
		}

		// Custom AI source configuration
		if (this.getEnv('CUSTOM_AI_API_KEY')) {
			sources.custom = {
				name: 'Custom AI',
				type: 'custom',
				apiKey: this.getEnv('CUSTOM_AI_API_KEY'),
				baseUrl: this.getEnv('CUSTOM_AI_BASE_URL'),
				model: this.getEnv('CUSTOM_AI_MODEL'),
				maxTokens: this.getEnvNumber('AI_MAX_TOKENS', 2048),
				temperature: parseFloat(this.getEnv('AI_TEMPERATURE', '0.8'))
			}
		}

		return sources
	}

	/**
	 * Load platform configurations
	 * @private
	 * @returns {Promise<Object>} Platform configurations
	 */
	async loadPlatformsConfig() {
		const platforms = {}

		// Discord configuration
		if (this.getEnv('DISCORD_BOT_TOKEN')) {
			platforms.discord = {
				token: this.getEnv('DISCORD_BOT_TOKEN'),
				clientId: this.getEnv('DISCORD_CLIENT_ID'),
				guildId: this.getEnv('DISCORD_GUILD_ID'),
				options: {}
			}
		}

		// Telegram configuration
		if (this.getEnv('TELEGRAM_BOT_TOKEN')) {
			platforms.telegram = {
				token: this.getEnv('TELEGRAM_BOT_TOKEN'),
				webhookUrl: this.getEnv('TELEGRAM_WEBHOOK_URL'),
				options: {}
			}
		}

		return platforms
	}

	/**
	 * Save configuration
	 * @param {Partial<AppConfig>} updates - Configuration updates
	 * @returns {Promise<void>}
	 */
	async save(updates) {
		if (!this.config) {
			await this.load()
		}

		// Merge updates
		this.config = { ...this.config, ...updates }

		// Save character configuration
		if (updates.character) {
			await DataManager.save('data/config/character.json', updates.character)
		}

		// Save AI sources configuration
		if (updates.aiSources) {
			await DataManager.save('data/config/ai-sources.json', updates.aiSources)
		}

		// Save platform configurations
		if (updates.platforms) {
			await DataManager.save('data/config/platforms.json', updates.platforms)
		}
	}

	/**
	 * Get current configuration
	 * @returns {AppConfig|null} Current configuration
	 */
	get() {
		return this.config
	}

	/**
	 * Reset configuration to defaults
	 * @returns {Promise<void>}
	 */
	async reset() {
		this.config = null
		await this.load()
	}
}

// Export singleton instance
export const configManager = new ConfigManager()

// Export class for testing
export { ConfigManager }
