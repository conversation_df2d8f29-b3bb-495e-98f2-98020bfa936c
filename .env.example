# Gentian Aphrodite Configuration Template
# Copy this file to .env and fill in your actual values

# =============================================================================
# AI Source Configuration
# =============================================================================

# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_BASE_URL=https://api.openai.com/v1
OPENAI_MODEL=gpt-4

# Claude Configuration (Anthropic)
CLAUDE_API_KEY=your_claude_api_key_here
CLAUDE_BASE_URL=https://api.anthropic.com
CLAUDE_MODEL=claude-3-sonnet-20240229

# Custom AI Source Configuration
CUSTOM_AI_API_KEY=your_custom_ai_api_key_here
CUSTOM_AI_BASE_URL=https://your-custom-ai-endpoint.com/v1
CUSTOM_AI_MODEL=your_model_name

# =============================================================================
# Platform Integration Configuration
# =============================================================================

# Discord Bot Configuration
DISCORD_BOT_TOKEN=your_discord_bot_token_here
DISCORD_CLIENT_ID=your_discord_client_id_here
DISCORD_GUILD_ID=your_discord_guild_id_here

# Telegram Bot Configuration
TELEGRAM_BOT_TOKEN=your_telegram_bot_token_here
TELEGRAM_WEBHOOK_URL=https://your-domain.com/webhook

# =============================================================================
# Application Configuration
# =============================================================================

# Application Settings
APP_NAME=Gentian Aphrodite
APP_VERSION=2.0.0
APP_ENVIRONMENT=development
APP_LOG_LEVEL=info

# Server Configuration
SERVER_PORT=3000
SERVER_HOST=localhost

# Data Storage Configuration
DATA_DIR=./data
MEMORY_DIR=./data/memory
CONFIG_DIR=./data/config
LOGS_DIR=./data/logs

# =============================================================================
# Character Configuration
# =============================================================================

# Character Settings
CHARACTER_NAME=龙胆•阿芙萝黛蒂
CHARACTER_USERNAME=GentianAphrodite
CHARACTER_LANGUAGE=zh-CN
CHARACTER_TIMEZONE=Asia/Shanghai

# AI Behavior Configuration
AI_MAX_TOKENS=2048
AI_TEMPERATURE=0.8
AI_TOP_P=0.9
AI_FREQUENCY_PENALTY=0.1
AI_PRESENCE_PENALTY=0.1

# Memory Configuration
MEMORY_MAX_ENTRIES=1000
MEMORY_CLEANUP_INTERVAL=86400000
MEMORY_RELEVANCE_THRESHOLD=0.3

# =============================================================================
# Security Configuration
# =============================================================================

# Security Settings
ENABLE_RATE_LIMITING=true
MAX_REQUESTS_PER_MINUTE=60
ENABLE_CONTENT_FILTERING=true

# Owner Configuration
OWNER_USER_ID=your_user_id_here
OWNER_USERNAME=your_username_here

# =============================================================================
# Development Configuration
# =============================================================================

# Debug Settings
DEBUG_MODE=false
VERBOSE_LOGGING=false
ENABLE_PERFORMANCE_MONITORING=false

# Development Tools
ENABLE_HOT_RELOAD=true
ENABLE_SOURCE_MAPS=true
