/**
 * Telegram Platform Tools
 * Utility functions for Telegram message processing and formatting
 */

/**
 * Split Telegram message content for character limit
 * @param {string} reply - Message content to split
 * @param {number} splitLength - Maximum length per message (default: 4096)
 * @returns {string[]} Array of split message parts
 */
export function splitTelegramReply(reply, splitLength = 4096) {
	if (!reply || reply.length <= splitLength) {
		return [reply || '']
	}

	const messages = []
	let currentMessage = ''
	const lines = reply.split('\n')

	for (const line of lines) {
		if (currentMessage.length + line.length + 1 > splitLength) {
			if (currentMessage) {
				messages.push(currentMessage)
				currentMessage = ''
			}
			
			// If single line is too long, split it
			if (line.length > splitLength) {
				const chunks = line.match(new RegExp(`.{1,${splitLength}}`, 'g')) || []
				messages.push(...chunks)
			} else {
				currentMessage = line
			}
		} else {
			currentMessage += (currentMessage ? '\n' : '') + line
		}
	}

	if (currentMessage) {
		messages.push(currentMessage)
	}

	return messages.filter(msg => msg.trim())
}

/**
 * Escape HTML special characters for Telegram
 * @param {string} text - Text to escape
 * @returns {string} Escaped text
 */
export function escapeHTML(text) {
	if (!text) return ''
	
	return text
		.replace(/&/g, '&amp;')
		.replace(/</g, '&lt;')
		.replace(/>/g, '&gt;')
		.replace(/"/g, '&quot;')
		.replace(/'/g, '&#39;')
}

/**
 * Convert AI markdown to Telegram HTML format
 * @param {string} aiMarkdownText - AI markdown text
 * @returns {string} Telegram HTML text
 */
export function aiMarkdownToTelegramHtml(aiMarkdownText) {
	if (!aiMarkdownText) return ''

	// First escape HTML characters
	let html = escapeHTML(aiMarkdownText)

	// Convert markdown formatting to HTML
	// Bold: **text** -> <b>text</b>
	html = html.replace(/\*\*(.*?)\*\*/g, '<b>$1</b>')
	
	// Italic: *text* -> <i>text</i>
	html = html.replace(/\*(.*?)\*/g, '<i>$1</i>')
	
	// Underline: __text__ -> <u>text</u>
	html = html.replace(/__(.*?)__/g, '<u>$1</u>')
	
	// Strikethrough: ~~text~~ -> <s>text</s>
	html = html.replace(/~~(.*?)~~/g, '<s>$1</s>')
	
	// Spoiler: ||text|| -> <span class="tg-spoiler">text</span>
	html = html.replace(/\|\|(.*?)\|\|/g, '<span class="tg-spoiler">$1</span>')
	
	// Inline code: `text` -> <code>text</code>
	html = html.replace(/`([^`]+)`/g, '<code>$1</code>')
	
	// Code blocks: ```text``` -> <pre>text</pre>
	html = html.replace(/```([\s\S]*?)```/g, '<pre>$1</pre>')
	
	// Links: [text](url) -> <a href="url">text</a>
	html = html.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2">$1</a>')

	return html
}

/**
 * Convert Telegram entities to AI markdown
 * @param {string} text - Original text
 * @param {Array} entities - Telegram message entities
 * @param {Object} botInfo - Bot information
 * @param {Object} replyToMessage - Reply to message object
 * @returns {string} AI markdown text
 */
export function telegramEntitiesToAiMarkdown(text, entities, botInfo, replyToMessage) {
	let aiMarkdown = ''

	// Handle reply to message
	if (replyToMessage) {
		const repliedFrom = replyToMessage.from
		let replierName = '未知用户'
		
		if (repliedFrom) {
			if (botInfo && repliedFrom.id === botInfo.id) {
				replierName = botInfo.first_name || botInfo.username || '我'
			} else {
				replierName = repliedFrom.first_name || repliedFrom.username || `User_${repliedFrom.id}`
			}
		}

		const repliedTextContent = replyToMessage.text || replyToMessage.caption || ''
		const repliedPreview = repliedTextContent.length > 100 
			? repliedTextContent.substring(0, 100) + '...' 
			: repliedTextContent

		if (repliedPreview) {
			aiMarkdown += repliedPreview.split('\n').map(line => `> ${line}`).join('\n')
			aiMarkdown += `\n(回复 ${replierName})\n\n`
		}
	}

	if (!text) return aiMarkdown.trim()

	const textChars = Array.from(text)
	if (!entities || entities.length === 0) {
		return aiMarkdown + text
	}

	const parts = []
	let lastOffset = 0

	// Sort entities by offset
	const sortedEntities = [...entities].sort((a, b) => a.offset - b.offset)

	for (const entity of sortedEntities) {
		// Add text before entity
		if (entity.offset > lastOffset) {
			parts.push(textChars.slice(lastOffset, entity.offset).join(''))
		}

		// Get entity text
		const entityText = textChars.slice(entity.offset, entity.offset + entity.length).join('')

		// Convert entity to markdown
		switch (entity.type) {
			case 'bold':
				parts.push(`**${entityText}**`)
				break
			case 'italic':
				parts.push(`*${entityText}*`)
				break
			case 'underline':
				parts.push(`__${entityText}__`)
				break
			case 'strikethrough':
				parts.push(`~~${entityText}~~`)
				break
			case 'spoiler':
				parts.push(`||${entityText}||`)
				break
			case 'code':
				parts.push(`\`${entityText}\``)
				break
			case 'pre':
				const language = entity.language || ''
				parts.push(`\`\`\`${language}\n${entityText}\n\`\`\``)
				break
			case 'text_link':
				parts.push(`[${entityText}](${entity.url})`)
				break
			case 'url':
				parts.push(`[${entityText}](${entityText})`)
				break
			case 'mention':
				parts.push(`@${entityText.substring(1)}`) // Remove @ from entityText
				break
			case 'text_mention':
				const user = entity.user
				const userName = user.first_name || user.username || `User_${user.id}`
				parts.push(`@[${userName} (UserID:${user.id})]`)
				break
			case 'hashtag':
			case 'cashtag':
			case 'bot_command':
			case 'email':
			case 'phone_number':
			default:
				parts.push(entityText)
				break
		}

		lastOffset = entity.offset + entity.length
	}

	// Add remaining text
	if (lastOffset < textChars.length) {
		parts.push(textChars.slice(lastOffset).join(''))
	}

	return aiMarkdown + parts.join('')
}

/**
 * Format Telegram message for display
 * @param {Object} message - Telegram message object
 * @returns {string} Formatted message
 */
export function formatTelegramMessage(message) {
	let content = ''

	// Handle different message types
	if (message.text) {
		content = message.text
	} else if (message.caption) {
		content = message.caption
	}

	// Add media type indicators
	if (message.photo) {
		content = `[图片] ${content}`.trim()
	} else if (message.video) {
		content = `[视频] ${content}`.trim()
	} else if (message.audio) {
		content = `[音频] ${content}`.trim()
	} else if (message.voice) {
		content = `[语音] ${content}`.trim()
	} else if (message.document) {
		const fileName = message.document.file_name || '文档'
		content = `[文档: ${fileName}] ${content}`.trim()
	} else if (message.sticker) {
		const emoji = message.sticker.emoji || '🎭'
		content = `[贴纸: ${emoji}] ${content}`.trim()
	} else if (message.animation) {
		content = `[动图] ${content}`.trim()
	} else if (message.location) {
		content = `[位置] ${content}`.trim()
	} else if (message.contact) {
		content = `[联系人] ${content}`.trim()
	}

	// Handle forwarded messages
	if (message.forward_from || message.forward_from_chat) {
		content = `[转发] ${content}`
	}

	// Handle edited messages
	if (message.edit_date) {
		content += ' (已编辑)'
	}

	return content
}

/**
 * Extract Telegram user ID from various formats
 * @param {string} input - Input string that might contain a user ID
 * @returns {number|null} Extracted user ID or null
 */
export function extractTelegramUserId(input) {
	if (!input) return null

	// Direct ID (numeric)
	const directMatch = input.match(/^\d+$/)
	if (directMatch) return parseInt(directMatch[0], 10)

	// Username format @username
	const usernameMatch = input.match(/^@?(\w+)$/)
	if (usernameMatch) {
		// Note: We can't convert username to ID without API call
		return null
	}

	// User mention format
	const mentionMatch = input.match(/UserID:(\d+)/)
	if (mentionMatch) return parseInt(mentionMatch[1], 10)

	return null
}

/**
 * Check if content is appropriate for Telegram
 * @param {string} content - Message content
 * @param {boolean} isChannel - Whether this is a channel message
 * @returns {Object} Appropriateness check result
 */
export function checkTelegramContentAppropriateness(content, isChannel = false) {
	const result = {
		appropriate: true,
		warnings: [],
		suggestions: []
	}

	// Check message length
	const maxLength = isChannel ? 4096 : 4096
	if (content.length > maxLength) {
		result.warnings.push('消息过长，可能需要分割')
		result.suggestions.push('考虑将长消息分成多个部分发送')
	}

	// Check for excessive formatting
	const htmlTags = (content.match(/<[^>]+>/g) || []).length
	if (htmlTags > content.length * 0.1) {
		result.warnings.push('HTML 标签过多')
		result.suggestions.push('减少 HTML 格式的使用')
	}

	// Check for spam-like content
	const repeatedChars = content.match(/(.)\1{10,}/g)
	if (repeatedChars) {
		result.warnings.push('包含重复字符，可能被视为垃圾信息')
		result.suggestions.push('避免过多重复字符')
	}

	return result
}
