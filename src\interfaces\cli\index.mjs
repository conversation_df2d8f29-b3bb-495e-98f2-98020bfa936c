/**
 * CLI Platform Interface
 * Provides command-line interaction with Gentian Aphrodite
 */

import { aiManager } from '../../core/ai-manager.mjs'

/**
 * @typedef {import('../../types/index.mjs').ChatMessage} ChatMessage
 * @typedef {import('../../types/index.mjs').PlatformInterface} PlatformInterface
 */

/**
 * CLI Platform implementation
 * @implements {PlatformInterface}
 */
export class CLIPlatform {
	/**
	 * @param {Object} config - CLI configuration
	 * @param {Object} app - Application instance
	 */
	constructor(config, app) {
		this.config = config
		this.app = app
		this.name = 'CLI'
		this.isRunning = false
		this.chatHistory = []
	}

	/**
	 * Start the CLI platform
	 * @returns {Promise<void>}
	 */
	async start() {
		this.isRunning = true
		console.log('💬 CLI Platform started. Type your messages below:')
		console.log('📝 Commands: /help, /status, /quit, /clear, /history')
		console.log('─'.repeat(50))

		await this.startInteractiveSession()
	}

	/**
	 * Start interactive CLI session
	 * @private
	 * @returns {Promise<void>}
	 */
	async startInteractiveSession() {
		const decoder = new TextDecoder()
		const encoder = new TextEncoder()

		while (this.isRunning) {
			try {
				// Display prompt
				await Deno.stdout.write(encoder.encode('\n🌸 You: '))

				// Read user input
				const buffer = new Uint8Array(1024)
				const bytesRead = await Deno.stdin.read(buffer)
				
				if (bytesRead === null) {
					// EOF reached
					break
				}

				const input = decoder.decode(buffer.subarray(0, bytesRead)).trim()

				if (!input) continue

				// Handle commands
				if (input.startsWith('/')) {
					await this.handleCommand(input)
					continue
				}

				// Process user message
				await this.processUserMessage(input)

			} catch (error) {
				console.error('CLI error:', error)
				if (error.name === 'Interrupted') {
					break
				}
			}
		}
	}

	/**
	 * Handle CLI commands
	 * @private
	 * @param {string} command - Command string
	 * @returns {Promise<void>}
	 */
	async handleCommand(command) {
		const [cmd, ...args] = command.slice(1).split(' ')

		switch (cmd.toLowerCase()) {
			case 'help':
				this.showHelp()
				break
			case 'status':
				await this.showStatus()
				break
			case 'quit':
			case 'exit':
				console.log('👋 Goodbye!')
				this.isRunning = false
				break
			case 'clear':
				console.clear()
				console.log('💬 CLI Platform - Chat cleared')
				break
			case 'history':
				this.showHistory()
				break
			case 'config':
				await this.showConfig()
				break
			case 'ai':
				await this.showAIInfo()
				break
			default:
				console.log(`❌ Unknown command: ${cmd}`)
				console.log('💡 Type /help for available commands')
		}
	}

	/**
	 * Show help information
	 * @private
	 */
	showHelp() {
		console.log('\n📖 Available Commands:')
		console.log('  /help     - Show this help message')
		console.log('  /status   - Show application status')
		console.log('  /config   - Show configuration info')
		console.log('  /ai       - Show AI sources info')
		console.log('  /history  - Show chat history')
		console.log('  /clear    - Clear the screen')
		console.log('  /quit     - Exit the application')
	}

	/**
	 * Show application status
	 * @private
	 * @returns {Promise<void>}
	 */
	async showStatus() {
		const status = this.app.getStatus()
		console.log('\n📊 Application Status:')
		console.log(`  Name: ${status.name}`)
		console.log(`  Version: ${status.version}`)
		console.log(`  Running: ${status.isRunning ? '✅' : '❌'}`)
		console.log(`  Uptime: ${Math.round(status.uptime / 1000)}s`)
		console.log(`  Platforms: ${status.platforms.join(', ') || 'None'}`)
		console.log(`  AI Sources: ${status.aiSources.length}`)
	}

	/**
	 * Show configuration information
	 * @private
	 * @returns {Promise<void>}
	 */
	async showConfig() {
		const config = this.app.config
		console.log('\n⚙️ Configuration:')
		console.log(`  Character: ${config.character.info.name}`)
		console.log(`  Language: ${config.character.info.defaultLocale}`)
		console.log(`  Environment: ${config.environment}`)
		console.log(`  Log Level: ${config.logLevel}`)
		console.log(`  Data Dir: ${config.storage.dataDir}`)
	}

	/**
	 * Show AI sources information
	 * @private
	 * @returns {Promise<void>}
	 */
	async showAIInfo() {
		const sources = aiManager.getSourcesInfo()
		console.log('\n🤖 AI Sources:')
		
		if (sources.length === 0) {
			console.log('  No AI sources configured')
			console.log('  💡 Please configure AI sources in your .env file')
		} else {
			for (const source of sources) {
				const status = source.available ? '✅' : '❌'
				console.log(`  ${status} ${source.name} (${source.type}) - ${source.model}`)
			}
		}

		const lastUsed = aiManager.getLastUsedSource()
		if (lastUsed) {
			console.log(`  🎯 Last used: ${lastUsed}`)
		}
	}

	/**
	 * Show chat history
	 * @private
	 */
	showHistory() {
		console.log('\n📜 Chat History:')
		
		if (this.chatHistory.length === 0) {
			console.log('  No messages yet')
			return
		}

		for (const message of this.chatHistory.slice(-10)) {
			const time = new Date(message.timestamp).toLocaleTimeString()
			const role = message.role === 'user' ? '🧑' : '🌸'
			console.log(`  [${time}] ${role} ${message.content.slice(0, 100)}${message.content.length > 100 ? '...' : ''}`)
		}

		if (this.chatHistory.length > 10) {
			console.log(`  ... and ${this.chatHistory.length - 10} more messages`)
		}
	}

	/**
	 * Process user message and generate response
	 * @private
	 * @param {string} content - User message content
	 * @returns {Promise<void>}
	 */
	async processUserMessage(content) {
		// Add user message to history
		const userMessage = {
			content,
			role: 'user',
			timestamp: Date.now(),
			name: 'User'
		}
		this.chatHistory.push(userMessage)

		try {
			// Show typing indicator
			console.log('🌸 龙胆•阿芙萝黛蒂 is typing...')

			// Generate response using legacy system
			const { GetReply } = await import('../../../reply_gener/index.mjs')
			const { GetPrompt } = await import('../../../prompt/index.mjs')

			// Build prompt using legacy system
			const prompt = await GetPrompt({
				chatId: 'cli',
				chatName: 'CLI Chat',
				username: 'User',
				characterName: this.app.config.character.info.name,
				locales: [this.app.config.character.info.defaultLocale],
				timestamp: Date.now(),
				chatLog: this.chatHistory.map(msg => ({
					content: msg.content,
					role: msg.role,
					timestamp: msg.timestamp,
					name: msg.name || (msg.role === 'user' ? 'User' : this.app.config.character.info.name)
				})),
				memory: {},
				extension: {}
			})

			// Generate reply
			const reply = await GetReply(prompt)

			// Add assistant message to history
			const assistantMessage = {
				content: reply.content,
				role: 'assistant',
				timestamp: Date.now(),
				name: this.app.config.character.info.name
			}
			this.chatHistory.push(assistantMessage)

			// Display response
			console.log(`🌸 ${this.app.config.character.info.name}: ${reply.content}`)

		} catch (error) {
			console.error('❌ Failed to generate response:', error.message)
			
			// Fallback response
			const fallbackMessage = {
				content: '抱歉，我现在无法回应。请检查AI源配置。',
				role: 'assistant',
				timestamp: Date.now(),
				name: this.app.config.character.info.name
			}
			this.chatHistory.push(fallbackMessage)
			console.log(`🌸 ${this.app.config.character.info.name}: ${fallbackMessage.content}`)
		}
	}

	/**
	 * Send message (CLI implementation)
	 * @param {string} channelId - Channel ID (unused in CLI)
	 * @param {Object} reply - Reply object
	 * @returns {Promise<void>}
	 */
	async sendMessage(channelId, reply) {
		console.log(`🌸 ${this.app.config.character.info.name}: ${reply.content}`)
	}

	/**
	 * Send typing indicator (CLI implementation)
	 * @param {string} channelId - Channel ID (unused in CLI)
	 * @returns {Promise<void>}
	 */
	async sendTyping(channelId) {
		console.log('🌸 龙胆•阿芙萝黛蒂 is typing...')
	}

	/**
	 * Get chat history (CLI implementation)
	 * @param {string} channelId - Channel ID (unused in CLI)
	 * @param {number} limit - Message limit
	 * @returns {Promise<ChatMessage[]>} Chat history
	 */
	async getHistory(channelId, limit) {
		return this.chatHistory.slice(-limit)
	}

	/**
	 * Get bot ID (CLI implementation)
	 * @returns {string} Bot ID
	 */
	getBotId() {
		return 'cli-bot'
	}

	/**
	 * Get bot name (CLI implementation)
	 * @returns {string} Bot name
	 */
	getBotName() {
		return this.app.config.character.info.name
	}

	/**
	 * Cleanup platform (CLI implementation)
	 * @returns {Promise<void>}
	 */
	async destroy() {
		this.isRunning = false
		console.log('🔌 CLI Platform shutdown')
	}
}
