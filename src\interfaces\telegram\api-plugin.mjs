/**
 * Telegram API Plugin
 * Provides Telegram-specific functionality for AI interactions
 */

/**
 * Keywords that might indicate Telegram-related operations
 */
const TELEGRAM_OPERATION_KEYWORDS = [
	'身份组', '群', '频道', '设置', '服务器', 'ban', '踢了', '禁言',
	'管理', '操作', '权限', '置顶', '分区', '分组', '帖子', '表情', '贴纸',
	'修改', '封禁', '邀请', '话题', '投票', '动态', '匿名',
	'删了', '删掉', 't.me', 'telegram.org', 'group', 'channel', 'chat',
	'kick', 'mute', 'restrict', 'permission', 'moderate', 'admin'
]

/**
 * Inappropriate content keywords (basic filtering)
 */
const INAPPROPRIATE_KEYWORDS = [
	'hack', 'exploit', 'spam', 'raid', 'flood', 'token',
	'破解', '攻击', '刷屏', '轰炸', '令牌', '恶意'
]

/**
 * Create Telegram API plugin for AI interactions
 * @param {import('telegraf').Telegraf} bot - Telegraf instance
 * @param {Object} message - Telegram message object
 * @returns {Object} Plugin API object
 */
export function createTelegramApiPlugin(bot, message) {
	return {
		info: {
			'zh-CN': {
				name: 'Telegram API 插件',
				description: 'Telegram API 插件，让 AI 能够进行 Telegram 相关操作',
				author: 'Gentian Aphrodite Team',
				version: '2.0.0'
			},
			'en-US': {
				name: 'Telegram API Plugin',
				description: 'Telegram API plugin for AI Telegram operations',
				author: 'Gentian Aphrodite Team',
				version: '2.0.0'
			}
		},
		interfaces: {
			chat: {
				/**
				 * Get JavaScript code prompt for Telegram operations
				 * @param {string} args - User input/request
				 * @param {any} result - Previous result
				 * @param {string} detailLevel - Detail level
				 * @returns {Promise<string>} Code prompt
				 */
				GetJSCodePrompt: async (args, result, detailLevel) => {
					// Check if request involves Telegram operations
					const hasTelegramKeywords = TELEGRAM_OPERATION_KEYWORDS.some(keyword =>
						args.toLowerCase().includes(keyword.toLowerCase())
					)

					// Check for inappropriate requests
					const hasInappropriateContent = INAPPROPRIATE_KEYWORDS.some(keyword =>
						args.toLowerCase().includes(keyword.toLowerCase())
					)

					if (hasInappropriateContent) {
						return null // Don't provide code for inappropriate requests
					}

					if (hasTelegramKeywords) {
						return `\
你可以使用以下变量来访问 Telegram API:
- message: 你正在回复的 Telegram 消息对象
- chat: 发生回复的 Telegram 群组/频道对象
- bot: Telegraf Bot 实例

可用的操作示例:
- 获取聊天信息: chat.id, chat.type, chat.title
- 获取用户信息: message.from.id, message.from.username, message.from.first_name
- 发送消息: bot.telegram.sendMessage(chat.id, "内容")
- 发送图片: bot.telegram.sendPhoto(chat.id, photo_url)
- 发送文档: bot.telegram.sendDocument(chat.id, document_url)
- 获取聊天成员: bot.telegram.getChatMember(chat.id, user_id)
- 限制用户: bot.telegram.restrictChatMember(chat.id, user_id, permissions)
- 踢出用户: bot.telegram.kickChatMember(chat.id, user_id)
- 设置聊天标题: bot.telegram.setChatTitle(chat.id, "新标题")
- 固定消息: bot.telegram.pinChatMessage(chat.id, message_id)

注意: 请谨慎使用管理功能，确保有适当的权限检查。`
					}

					return null
				},

				/**
				 * Get JavaScript code execution context
				 * @param {string} args - User input/request
				 * @param {any} result - Previous result
				 * @param {string} detailLevel - Detail level
				 * @returns {Promise<Object>} Execution context
				 */
				GetJSCodeContext: async (args, result, detailLevel) => {
					return {
						message,
						chat: message.chat,
						bot: bot,
						// Utility functions
						utils: {
							/**
							 * Safely get chat member
							 * @param {number} chatId - Chat ID
							 * @param {number} userId - User ID
							 * @returns {Promise<any>} Chat member object or null
							 */
							getChatMember: async (chatId, userId) => {
								try {
									return await bot.telegram.getChatMember(chatId, userId)
								} catch (error) {
									console.warn('Failed to get chat member:', error)
									return null
								}
							},

							/**
							 * Check if user has admin permissions
							 * @param {number} chatId - Chat ID
							 * @param {number} userId - User ID
							 * @returns {Promise<boolean>} True if user is admin
							 */
							isAdmin: async (chatId, userId) => {
								try {
									const member = await bot.telegram.getChatMember(chatId, userId)
									return member && ['creator', 'administrator'].includes(member.status)
								} catch (error) {
									console.warn('Failed to check admin status:', error)
									return false
								}
							},

							/**
							 * Check if bot has admin permissions
							 * @param {number} chatId - Chat ID
							 * @returns {Promise<boolean>} True if bot is admin
							 */
							isBotAdmin: async (chatId) => {
								try {
									const botInfo = await bot.telegram.getMe()
									const member = await bot.telegram.getChatMember(chatId, botInfo.id)
									return member && ['creator', 'administrator'].includes(member.status)
								} catch (error) {
									console.warn('Failed to check bot admin status:', error)
									return false
								}
							},

							/**
							 * Get chat administrators
							 * @param {number} chatId - Chat ID
							 * @returns {Promise<Array>} Array of administrators
							 */
							getChatAdministrators: async (chatId) => {
								try {
									return await bot.telegram.getChatAdministrators(chatId)
								} catch (error) {
									console.warn('Failed to get chat administrators:', error)
									return []
								}
							},

							/**
							 * Format user mention
							 * @param {Object} user - User object
							 * @returns {string} Formatted mention
							 */
							mentionUser: (user) => {
								if (user.username) {
									return `@${user.username}`
								} else {
									return `[${user.first_name || 'User'}](tg://user?id=${user.id})`
								}
							},

							/**
							 * Get current timestamp
							 * @returns {number} Current timestamp
							 */
							now: () => Math.floor(Date.now() / 1000),

							/**
							 * Sleep for specified milliseconds
							 * @param {number} ms - Milliseconds to sleep
							 * @returns {Promise<void>}
							 */
							sleep: (ms) => new Promise(resolve => setTimeout(resolve, ms)),

							/**
							 * Parse chat permissions
							 * @param {Object} permissions - Telegram chat permissions
							 * @returns {Object} Parsed permissions
							 */
							parsePermissions: (permissions) => {
								return {
									canSendMessages: permissions.can_send_messages !== false,
									canSendMediaMessages: permissions.can_send_media_messages !== false,
									canSendPolls: permissions.can_send_polls !== false,
									canSendOtherMessages: permissions.can_send_other_messages !== false,
									canAddWebPagePreviews: permissions.can_add_web_page_previews !== false,
									canChangeInfo: permissions.can_change_info !== false,
									canInviteUsers: permissions.can_invite_users !== false,
									canPinMessages: permissions.can_pin_messages !== false
								}
							},

							/**
							 * Create restricted permissions
							 * @param {Object} options - Permission options
							 * @returns {Object} Telegram permissions object
							 */
							createPermissions: (options = {}) => {
								return {
									can_send_messages: options.canSendMessages !== false,
									can_send_media_messages: options.canSendMediaMessages !== false,
									can_send_polls: options.canSendPolls !== false,
									can_send_other_messages: options.canSendOtherMessages !== false,
									can_add_web_page_previews: options.canAddWebPagePreviews !== false,
									can_change_info: options.canChangeInfo !== false,
									can_invite_users: options.canInviteUsers !== false,
									can_pin_messages: options.canPinMessages !== false
								}
							}
						}
					}
				}
			}
		}
	}
}

/**
 * Check if message content suggests Telegram API usage
 * @param {string} content - Message content
 * @returns {boolean} True if likely needs Telegram API
 */
export function needsTelegramApi(content) {
	if (!content) return false

	const lowerContent = content.toLowerCase()
	
	return TELEGRAM_OPERATION_KEYWORDS.some(keyword =>
		lowerContent.includes(keyword.toLowerCase())
	)
}

/**
 * Check if request is appropriate for Telegram API
 * @param {string} content - Message content
 * @returns {boolean} True if appropriate
 */
export function isAppropriateRequest(content) {
	if (!content) return true

	const lowerContent = content.toLowerCase()
	
	return !INAPPROPRIATE_KEYWORDS.some(keyword =>
		lowerContent.includes(keyword.toLowerCase())
	)
}

/**
 * Get Telegram API capabilities description
 * @returns {string} Capabilities description
 */
export function getApiCapabilities() {
	return `\
Telegram API 插件功能:
- 聊天管理: 获取聊天信息、发送消息、管理权限
- 用户管理: 获取用户信息、检查权限、管理成员
- 群组管理: 获取群组信息、管理管理员、设置群组属性
- 消息处理: 发送、编辑、删除消息，固定消息
- 权限检查: 验证用户权限、管理员状态检查
- 媒体处理: 发送图片、视频、文档等媒体文件

注意事项:
- 所有操作都需要适当的权限
- 不支持破坏性或恶意操作
- 遵循 Telegram 服务条款和社区准则
- 管理操作需要 Bot 具有管理员权限`
}
