/**
 * AI Source management system
 * Independent implementation replacing fount platform dependencies
 */

import { configManager } from './config.mjs'

/**
 * @typedef {import('../types/index.mjs').AISource} AISource
 * @typedef {import('../types/index.mjs').AISourceConfig} AISourceConfig
 */

/**
 * OpenAI-compatible AI source implementation
 */
class OpenAISource {
	/**
	 * @param {AISourceConfig} config - AI source configuration
	 */
	constructor(config) {
		this.config = config
		this.name = config.name
		this.type = config.type
	}

	/**
	 * Call the AI source
	 * @param {string} prompt - Input prompt
	 * @param {Object} [options] - Additional options
	 * @returns {Promise<string>} AI response
	 */
	async call(prompt, options = {}) {
		const requestBody = {
			model: this.config.model,
			messages: [
				{
					role: 'user',
					content: prompt
				}
			],
			max_tokens: options.maxTokens || this.config.maxTokens || 2048,
			temperature: options.temperature || this.config.temperature || 0.8,
			top_p: options.topP || this.config.topP || 0.9,
			frequency_penalty: options.frequencyPenalty || this.config.frequencyPenalty || 0.1,
			presence_penalty: options.presencePenalty || this.config.presencePenalty || 0.1
		}

		const response = await fetch(`${this.config.baseUrl}/chat/completions`, {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				'Authorization': `Bearer ${this.config.apiKey}`
			},
			body: JSON.stringify(requestBody)
		})

		if (!response.ok) {
			const errorText = await response.text()
			throw new Error(`OpenAI API error: ${response.status} ${response.statusText} - ${errorText}`)
		}

		const data = await response.json()
		
		if (!data.choices || data.choices.length === 0) {
			throw new Error('No response from OpenAI API')
		}

		return data.choices[0].message.content
	}

	/**
	 * Check if the AI source is available
	 * @returns {boolean} True if available
	 */
	isAvailable() {
		return !!(this.config.apiKey && this.config.baseUrl && this.config.model)
	}

	/**
	 * Get source information
	 * @returns {Object} Source info
	 */
	getInfo() {
		return {
			name: this.name,
			type: this.type,
			model: this.config.model,
			available: this.isAvailable()
		}
	}
}

/**
 * Claude (Anthropic) AI source implementation
 */
class ClaudeSource {
	/**
	 * @param {AISourceConfig} config - AI source configuration
	 */
	constructor(config) {
		this.config = config
		this.name = config.name
		this.type = config.type
	}

	/**
	 * Call the AI source
	 * @param {string} prompt - Input prompt
	 * @param {Object} [options] - Additional options
	 * @returns {Promise<string>} AI response
	 */
	async call(prompt, options = {}) {
		const requestBody = {
			model: this.config.model,
			max_tokens: options.maxTokens || this.config.maxTokens || 2048,
			temperature: options.temperature || this.config.temperature || 0.8,
			messages: [
				{
					role: 'user',
					content: prompt
				}
			]
		}

		const response = await fetch(`${this.config.baseUrl}/v1/messages`, {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				'x-api-key': this.config.apiKey,
				'anthropic-version': '2023-06-01'
			},
			body: JSON.stringify(requestBody)
		})

		if (!response.ok) {
			const errorText = await response.text()
			throw new Error(`Claude API error: ${response.status} ${response.statusText} - ${errorText}`)
		}

		const data = await response.json()
		
		if (!data.content || data.content.length === 0) {
			throw new Error('No response from Claude API')
		}

		return data.content[0].text
	}

	/**
	 * Check if the AI source is available
	 * @returns {boolean} True if available
	 */
	isAvailable() {
		return !!(this.config.apiKey && this.config.baseUrl && this.config.model)
	}

	/**
	 * Get source information
	 * @returns {Object} Source info
	 */
	getInfo() {
		return {
			name: this.name,
			type: this.type,
			model: this.config.model,
			available: this.isAvailable()
		}
	}
}

/**
 * Custom AI source implementation for other APIs
 */
class CustomSource {
	/**
	 * @param {AISourceConfig} config - AI source configuration
	 */
	constructor(config) {
		this.config = config
		this.name = config.name
		this.type = config.type
	}

	/**
	 * Call the AI source
	 * @param {string} prompt - Input prompt
	 * @param {Object} [options] - Additional options
	 * @returns {Promise<string>} AI response
	 */
	async call(prompt, options = {}) {
		// Default to OpenAI-compatible format for custom sources
		const requestBody = {
			model: this.config.model,
			messages: [
				{
					role: 'user',
					content: prompt
				}
			],
			max_tokens: options.maxTokens || this.config.maxTokens || 2048,
			temperature: options.temperature || this.config.temperature || 0.8,
			...this.config.options
		}

		const headers = {
			'Content-Type': 'application/json',
			...this.config.headers
		}

		// Add authorization header
		if (this.config.apiKey) {
			headers['Authorization'] = `Bearer ${this.config.apiKey}`
		}

		const response = await fetch(`${this.config.baseUrl}/chat/completions`, {
			method: 'POST',
			headers,
			body: JSON.stringify(requestBody)
		})

		if (!response.ok) {
			const errorText = await response.text()
			throw new Error(`Custom AI API error: ${response.status} ${response.statusText} - ${errorText}`)
		}

		const data = await response.json()
		
		// Try different response formats
		if (data.choices && data.choices.length > 0) {
			return data.choices[0].message.content
		} else if (data.content) {
			return Array.isArray(data.content) ? data.content[0].text : data.content
		} else if (data.response) {
			return data.response
		} else if (data.text) {
			return data.text
		}

		throw new Error('Unexpected response format from custom AI API')
	}

	/**
	 * Check if the AI source is available
	 * @returns {boolean} True if available
	 */
	isAvailable() {
		return !!(this.config.baseUrl && this.config.model)
	}

	/**
	 * Get source information
	 * @returns {Object} Source info
	 */
	getInfo() {
		return {
			name: this.name,
			type: this.type,
			model: this.config.model,
			available: this.isAvailable()
		}
	}
}

/**
 * AI Manager class for handling multiple AI sources
 */
class AIManager {
	constructor() {
		/** @type {Map<string, AISource>} */
		this.sources = new Map()
		this.lastUsedSource = null
		this.initialized = false
	}

	/**
	 * Initialize AI manager with configuration
	 * @returns {Promise<void>}
	 */
	async initialize() {
		if (this.initialized) return

		const config = await configManager.load()
		const aiSourcesConfig = config.aiSources || {}

		// Clear existing sources
		this.sources.clear()

		// Initialize AI sources based on configuration
		for (const [key, sourceConfig] of Object.entries(aiSourcesConfig)) {
			if (sourceConfig && sourceConfig.apiKey) {
				try {
					const source = this.createSource(sourceConfig)
					if (source.isAvailable()) {
						this.sources.set(key, source)
						console.log(`Initialized AI source: ${key} (${sourceConfig.type})`)
					}
				} catch (error) {
					console.error(`Failed to initialize AI source ${key}:`, error)
				}
			}
		}

		this.initialized = true
		console.log(`AI Manager initialized with ${this.sources.size} sources`)
	}

	/**
	 * Create AI source instance based on configuration
	 * @param {AISourceConfig} config - Source configuration
	 * @returns {AISource} AI source instance
	 */
	createSource(config) {
		switch (config.type) {
			case 'openai':
				return new OpenAISource(config)
			case 'claude':
				return new ClaudeSource(config)
			case 'custom':
				return new CustomSource(config)
			default:
				throw new Error(`Unknown AI source type: ${config.type}`)
		}
	}

	/**
	 * Get AI source calling order based on task type
	 * @param {string} taskType - Task type (detail-thinking, web-browse, expert, sfw, nsfw, logic)
	 * @returns {string[]} Ordered list of source names
	 */
	getCallingOrder(taskType) {
		const allSources = Array.from(this.sources.keys())
		
		switch (taskType) {
			case 'detail-thinking':
				return ['detail-thinking', 'expert', 'sfw', 'web-browse', 'nsfw', 'logic'].filter(name => allSources.includes(name))
			case 'web-browse':
				return ['web-browse', 'detail-thinking', 'expert', 'sfw', 'nsfw', 'logic'].filter(name => allSources.includes(name))
			case 'expert':
				return ['expert', 'detail-thinking', 'sfw', 'web-browse', 'nsfw', 'logic'].filter(name => allSources.includes(name))
			case 'sfw':
				return ['sfw', 'expert', 'detail-thinking', 'web-browse', 'nsfw', 'logic'].filter(name => allSources.includes(name))
			case 'nsfw':
				return ['nsfw', 'logic', 'web-browse', 'sfw', 'expert', 'detail-thinking'].filter(name => allSources.includes(name))
			case 'logic':
				return ['logic', 'nsfw', 'web-browse', 'sfw', 'expert', 'detail-thinking'].filter(name => allSources.includes(name))
			default:
				return allSources
		}
	}

	/**
	 * Call AI with ordered fallback
	 * @param {string} taskType - Task type
	 * @param {string} prompt - Input prompt
	 * @param {Object} [options] - Additional options
	 * @param {number} [retries] - Number of retries per source
	 * @returns {Promise<{content: string, source: string}>} AI response with source info
	 */
	async call(taskType, prompt, options = {}, retries = 3) {
		if (!this.initialized) {
			await this.initialize()
		}

		const callingOrder = this.getCallingOrder(taskType)
		
		if (callingOrder.length === 0) {
			throw new Error('No AI sources available')
		}

		let lastError = new Error('No AI sources available')

		for (const sourceName of callingOrder) {
			const source = this.sources.get(sourceName)
			if (!source || !source.isAvailable()) {
				continue
			}

			for (let i = 0; i < retries; i++) {
				try {
					console.log(`Calling AI source: ${sourceName} (attempt ${i + 1}/${retries})`)
					const content = await source.call(prompt, options)
					
					if (content && content.trim()) {
						this.lastUsedSource = sourceName
						return {
							content: content.trim(),
							source: sourceName
						}
					} else {
						throw new Error('Empty response from AI')
					}
				} catch (error) {
					lastError = error
					console.error(`AI source ${sourceName} failed (attempt ${i + 1}/${retries}):`, error.message)
					
					// Wait before retry
					if (i < retries - 1) {
						await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)))
					}
				}
			}
		}

		throw lastError
	}

	/**
	 * Check if any AI sources are available
	 * @returns {boolean} True if at least one source is available
	 */
	hasAvailableSources() {
		return this.sources.size > 0
	}

	/**
	 * Get information about all sources
	 * @returns {Object[]} Array of source information
	 */
	getSourcesInfo() {
		const info = []
		for (const [name, source] of this.sources) {
			info.push({
				name,
				...source.getInfo()
			})
		}
		return info
	}

	/**
	 * Get last used source name
	 * @returns {string|null} Last used source name
	 */
	getLastUsedSource() {
		return this.lastUsedSource
	}

	/**
	 * Reload sources from configuration
	 * @returns {Promise<void>}
	 */
	async reload() {
		this.initialized = false
		await this.initialize()
	}
}

// Export singleton instance
export const aiManager = new AIManager()

// Export class for testing
export { AIManager }
