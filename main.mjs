/**
 * Gentian Aphrodite - Independent AI Character Companion
 * Powered by <PERSON><PERSON>, independent from fount platform
 */

import { configManager } from './src/core/config.mjs'
import { aiManager } from './src/core/ai-manager.mjs'
import { DataManager } from './src/core/data-manager.mjs'

// Legacy imports for compatibility
import { GetGreeting, GetGroupGreeting } from './greetings/index.mjs'
import { GetPrompt, GetPromptForOther } from './prompt/index.mjs'
import { GetReply } from './reply_gener/index.mjs'
import { UpdateInfo } from './info/index.mjs'
import { GetData, SetData } from './config.mjs'
import { saveMemorys } from './prompt/memory/index.mjs'
import { timerCallBack } from './reply_gener/functions/timer.mjs'
import { saveVars } from './scripts/vars.mjs'

/**
 * @typedef {import('./src/types/index.mjs').AppConfig} AppConfig
 * @typedef {import('./src/types/index.mjs').PlatformInterface} PlatformInterface
 */

/**
 * Application class for managing the AI character
 */
class GentianAphroditeApp {
	constructor() {
		this.config = null
		this.platforms = new Map()
		this.isRunning = false
		this.startTime = Date.now()
	}

	/**
	 * Initialize the application
	 * @returns {Promise<void>}
	 */
	async initialize() {
		console.log('🌸 Initializing Gentian Aphrodite...')

		// Load configuration
		this.config = await configManager.load()
		console.log(`📋 Configuration loaded (${this.config.name} v${this.config.version})`)

		// Initialize AI manager
		await aiManager.initialize()
		console.log('🤖 AI Manager initialized')

		// Ensure data directories exist
		await this.ensureDirectories()
		console.log('📁 Data directories ensured')

		// Initialize character base (legacy compatibility)
		await this.initCharBase()
		console.log('👤 Character base initialized')

		console.log('✅ Gentian Aphrodite initialized successfully')
	}

	/**
	 * Ensure required directories exist
	 * @private
	 * @returns {Promise<void>}
	 */
	async ensureDirectories() {
		const dirs = [
			this.config.storage.dataDir,
			this.config.storage.memoryDir,
			this.config.storage.configDir,
			this.config.storage.logsDir
		]

		for (const dir of dirs) {
			await DataManager.ensureDir(dir)
		}
	}

	/**
	 * Initialize character base for legacy compatibility
	 * @private
	 * @returns {Promise<void>}
	 */
	async initCharBase() {
		// Legacy compatibility - simulate fount platform initialization
		const legacyInit = {
			username: this.config.character.info.username || 'GentianAphrodite'
		}

		// Import and call legacy initCharBase if it exists
		try {
			const { initCharBase } = await import('./charbase.mjs')
			await initCharBase(legacyInit)
		} catch (error) {
			console.warn('Legacy charbase initialization failed:', error.message)
		}
	}

	/**
	 * Start the application with specified platform
	 * @param {string} platform - Platform name (discord, telegram, cli)
	 * @returns {Promise<void>}
	 */
	async start(platform = 'cli') {
		if (this.isRunning) {
			console.warn('Application is already running')
			return
		}

		await this.initialize()

		console.log(`🚀 Starting ${platform} platform...`)

		switch (platform.toLowerCase()) {
			case 'discord':
				await this.startDiscord()
				break
			case 'telegram':
				await this.startTelegram()
				break
			case 'cli':
				await this.startCLI()
				break
			default:
				throw new Error(`Unknown platform: ${platform}`)
		}

		this.isRunning = true
		console.log(`🎉 ${platform} platform started successfully`)

		// Setup graceful shutdown
		this.setupGracefulShutdown()
	}

	/**
	 * Start Discord platform
	 * @private
	 * @returns {Promise<void>}
	 */
	async startDiscord() {
		if (!this.config.platforms.discord) {
			throw new Error('Discord configuration not found. Please set DISCORD_BOT_TOKEN in your .env file.')
		}

		try {
			const { DiscordPlatform } = await import('./src/interfaces/discord/index.mjs')
			const discordPlatform = new DiscordPlatform(this.config.platforms.discord, this)
			await discordPlatform.start()
			this.platforms.set('discord', discordPlatform)
		} catch (error) {
			console.error('Failed to start Discord platform:', error)
			throw error
		}
	}

	/**
	 * Start Telegram platform
	 * @private
	 * @returns {Promise<void>}
	 */
	async startTelegram() {
		if (!this.config.platforms.telegram) {
			throw new Error('Telegram configuration not found. Please set TELEGRAM_BOT_TOKEN in your .env file.')
		}

		try {
			const { TelegramPlatform } = await import('./src/interfaces/telegram/index.mjs')
			const telegramPlatform = new TelegramPlatform(this.config.platforms.telegram, this)
			await telegramPlatform.start()
			this.platforms.set('telegram', telegramPlatform)
		} catch (error) {
			console.error('Failed to start Telegram platform:', error)
			throw error
		}
	}

	/**
	 * Start CLI platform
	 * @private
	 * @returns {Promise<void>}
	 */
	async startCLI() {
		try {
			const { CLIPlatform } = await import('./src/interfaces/cli/index.mjs')
			const cliPlatform = new CLIPlatform({}, this)
			await cliPlatform.start()
			this.platforms.set('cli', cliPlatform)
		} catch (error) {
			console.error('Failed to start CLI platform:', error)
			throw error
		}
	}

	/**
	 * Setup graceful shutdown handlers
	 * @private
	 */
	setupGracefulShutdown() {
		const shutdown = async (signal) => {
			console.log(`\n🛑 Received ${signal}, shutting down gracefully...`)
			await this.shutdown()
			Deno.exit(0)
		}

		// Handle various shutdown signals
		Deno.addSignalListener('SIGINT', () => shutdown('SIGINT'))
		Deno.addSignalListener('SIGTERM', () => shutdown('SIGTERM'))

		// Handle unhandled errors
		globalThis.addEventListener('unhandledrejection', (event) => {
			console.error('Unhandled promise rejection:', event.reason)
			event.preventDefault()
		})

		globalThis.addEventListener('error', (event) => {
			console.error('Unhandled error:', event.error)
		})
	}

	/**
	 * Shutdown the application
	 * @returns {Promise<void>}
	 */
	async shutdown() {
		if (!this.isRunning) return

		console.log('💾 Saving data before shutdown...')

		try {
			// Save memories and variables (legacy compatibility)
			await saveMemorys()
			await saveVars()

			// Shutdown all platforms
			for (const [name, platform] of this.platforms) {
				console.log(`🔌 Shutting down ${name} platform...`)
				try {
					await platform.destroy()
				} catch (error) {
					console.error(`Failed to shutdown ${name} platform:`, error)
				}
			}

			this.platforms.clear()
			this.isRunning = false

			const uptime = Date.now() - this.startTime
			console.log(`👋 Gentian Aphrodite shutdown complete (uptime: ${Math.round(uptime / 1000)}s)`)
		} catch (error) {
			console.error('Error during shutdown:', error)
		}
	}

	/**
	 * Get application status
	 * @returns {Object} Application status
	 */
	getStatus() {
		return {
			name: this.config?.name || 'Gentian Aphrodite',
			version: this.config?.version || '2.0.0',
			isRunning: this.isRunning,
			uptime: this.isRunning ? Date.now() - this.startTime : 0,
			platforms: Array.from(this.platforms.keys()),
			aiSources: aiManager.getSourcesInfo()
		}
	}

	/**
	 * Legacy API compatibility - Get character info
	 * @returns {Promise<Object>} Character information
	 */
	async getCharacterInfo() {
		try {
			return await UpdateInfo()
		} catch (error) {
			console.error('Failed to get character info:', error)
			return {
				name: this.config?.character?.info?.name || '龙胆•阿芙萝黛蒂',
				version: this.config?.version || '2.0.0',
				description: this.config?.character?.info?.description || 'AI Character Companion'
			}
		}
	}

	/**
	 * Legacy API compatibility - Get configuration data
	 * @param {string} key - Configuration key
	 * @returns {Promise<any>} Configuration value
	 */
	async getConfigData(key) {
		try {
			return await GetData(key)
		} catch (error) {
			console.error(`Failed to get config data for key ${key}:`, error)
			return null
		}
	}

	/**
	 * Legacy API compatibility - Set configuration data
	 * @param {string} key - Configuration key
	 * @param {any} value - Configuration value
	 * @returns {Promise<void>}
	 */
	async setConfigData(key, value) {
		try {
			await SetData(key, value)
		} catch (error) {
			console.error(`Failed to set config data for key ${key}:`, error)
		}
	}
}

// Create global application instance
const app = new GentianAphroditeApp()

// Command line interface
if (import.meta.main) {
	const args = Deno.args
	let platform = 'cli'

	// Parse command line arguments
	for (let i = 0; i < args.length; i++) {
		const arg = args[i]
		if (arg === '--platform' && i + 1 < args.length) {
			platform = args[i + 1]
			i++ // Skip next argument
		} else if (arg.startsWith('--platform=')) {
			platform = arg.split('=')[1]
		}
	}

	// Start the application
	try {
		await app.start(platform)
	} catch (error) {
		console.error('Failed to start application:', error)
		Deno.exit(1)
	}
}

// Export for use as module
export default app
export { GentianAphroditeApp }
