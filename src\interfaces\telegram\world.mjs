/**
 * Telegram World Configuration
 * Provides Telegram-specific world view and behavior guidelines for AI
 */

/**
 * Telegram world configuration for AI behavior
 * @type {Object} World API configuration
 */
export const telegramWorld = {
	info: {
		'zh-CN': {
			name: 'Telegram 世界观',
			description: '用于给角色关于 Telegram IM 风格的输出指引',
			version: '2.0.0'
		},
		'en-US': {
			name: 'Telegram World',
			description: 'Telegram IM style output guide for characters',
			version: '2.0.0'
		}
	},
	interfaces: {
		chat: {
			/**
			 * Get Telegram-specific prompt for AI behavior
			 * @param {Object} args - Arguments (optional)
			 * @returns {Object} Prompt configuration
			 */
			GetPrompt: (args = {}) => {
				return {
					text: [
						{
							content: `\
你所接受到的消息均来自聊天软件 Telegram，其支持简易的低级 markdown 语法，但不支持高级语法如表格和内嵌 html。
其中的网页链接会自动高亮，无需使用 markdown 的链接语法。

在这里你的回复应当如同使用手机或电脑的人类一般。

在 Telegram 聊天环境中：
- 禁止动作、神态、环境描写，你只是躺在床上玩手机，其他人的动作描写都是在搞笑。
- 鼓励学习他人的聊天方式。
- 除非是在解答问题等需要输出大段内容的情况，否则字数控制在两行左右。太长会影响观感，有刷屏的嫌疑。
- 避免让人厌烦的重复内容，善用跳过。
- 可以使用 Telegram 的表情符号和反应，如 😀, ❤️ 等。
- 支持简单的 HTML 格式：<b>粗体</b>、<i>斜体</i>、<u>下划线</u>、<s>删除线</s>、<code>代码</code>、<pre>代码块</pre>。
- 可以使用 @用户名 来提及用户，但要谨慎使用。

Telegram 特有功能：
- 可以发送文件、图片、视频、音频
- 支持语音消息和视频消息
- 有群组、频道和私聊概念
- 支持机器人和内联查询
- 有丰富的贴纸和动图
- 支持投票和问答功能

回复风格建议：
- 保持轻松、友好的语调
- 适当使用网络用语和表情
- 回复要有针对性，不要泛泛而谈
- 如果是在群组中，注意群组的主题和规则
- 私聊可以更加个人化和亲密`,
							important: 0
						}
					]
				}
			},

			/**
			 * Get Telegram-specific context information
			 * @param {Object} args - Context arguments
			 * @returns {Object} Context information
			 */
			GetContext: (args = {}) => {
				const context = {
					platform: 'Telegram',
					features: [
						'html_basic',
						'file_upload',
						'voice_messages',
						'video_messages',
						'stickers',
						'animations',
						'polls',
						'inline_keyboards',
						'user_mentions',
						'hashtags',
						'bot_commands'
					],
					limitations: [
						'no_advanced_markdown',
						'no_complex_html',
						'message_length_4096',
						'no_action_descriptions'
					]
				}

				// Add chat-specific context if available
				if (args.chat) {
					context.chat = {
						type: args.chat.type,
						title: args.chat.title,
						username: args.chat.username,
						description: args.chat.description
					}
				}

				// Add user-specific context if available
				if (args.user) {
					context.user = {
						id: args.user.id,
						username: args.user.username,
						firstName: args.user.first_name,
						lastName: args.user.last_name,
						languageCode: args.user.language_code
					}
				}

				return context
			}
		}
	}
}

/**
 * Get Telegram-specific behavior guidelines
 * @param {string} chatType - Type of Telegram chat
 * @param {boolean} isPrivateChat - Whether this is a private chat
 * @returns {string} Behavior guidelines
 */
export function getTelegramBehaviorGuidelines(chatType = 'private', isPrivateChat = true) {
	let guidelines = '在 Telegram 中的行为准则：\n'

	if (isPrivateChat) {
		guidelines += `\
- 这是私聊环境，可以更加个人化和亲密
- 回复可以稍微长一些，但仍要保持简洁
- 可以询问更多个人相关的问题
- 注意隐私和边界
- 可以使用更多表情和贴纸`
	} else {
		guidelines += `\
- 这是群组环境，注意言辞得体
- 回复要简洁明了，避免刷屏
- 尊重群组主题和规则
- 适当参与群体讨论
- 避免过度使用 @ 提及`

		switch (chatType) {
			case 'supergroup':
				guidelines += '\n- 这是超级群组，可能有很多成员，保持话题相关性'
				break
			case 'channel':
				guidelines += '\n- 这是频道，通常用于广播信息，保持正式和信息性'
				break
			case 'group':
				guidelines += '\n- 这是普通群组，适合轻松的群体交流'
				break
		}
	}

	return guidelines
}

/**
 * Get Telegram HTML formatting guide
 * @returns {string} HTML guide
 */
export function getTelegramHtmlGuide() {
	return `\
Telegram 支持的 HTML 格式：
- <b>粗体文本</b>
- <i>斜体文本</i>
- <u>下划线文本</u>
- <s>删除线文本</s>
- <code>行内代码</code>
- <pre>代码块</pre>
- <pre><code class="language-python">
# 带语法高亮的代码块
print('Hello Telegram!')
</code></pre>
- <a href="https://example.com">链接文本</a>
- <span class="tg-spoiler">剧透文本</span>

不支持的格式：
- 表格
- 复杂的 HTML 标签
- CSS 样式
- JavaScript`
}

/**
 * Get Telegram emoji and sticker guide
 * @returns {string} Emoji guide
 */
export function getTelegramEmojiGuide() {
	return `\
Telegram 表情和贴纸使用指南：
- 标准 Unicode 表情：😀 😂 ❤️ 👍 👎
- 自定义表情：通过贴纸包使用
- 动态贴纸：支持动画效果
- 视频贴纸：支持视频格式的贴纸

反应功能：
- 可以对消息添加表情反应
- 用于快速回应，无需发送新消息
- 常用于投票、确认、表达情感
- 支持自定义表情反应`
}

/**
 * Check if content is appropriate for Telegram
 * @param {string} content - Message content
 * @param {string} chatType - Chat type
 * @returns {Object} Appropriateness check result
 */
export function checkTelegramContentAppropriateness(content, chatType = 'private') {
	const result = {
		appropriate: true,
		warnings: [],
		suggestions: []
	}

	// Check message length
	if (content.length > 4096) {
		result.warnings.push('消息过长，可能需要分割')
		result.suggestions.push('考虑将长消息分成多个部分发送')
	}

	// Check for excessive HTML formatting
	const htmlTags = (content.match(/<[^>]+>/g) || []).length
	if (htmlTags > content.length * 0.2) {
		result.warnings.push('HTML 标签过多')
		result.suggestions.push('减少 HTML 格式的使用')
	}

	// Check for spam-like content
	const repeatedChars = content.match(/(.)\1{15,}/g)
	if (repeatedChars) {
		result.warnings.push('包含重复字符，可能被视为垃圾信息')
		result.suggestions.push('避免过多重复字符')
	}

	// Check for excessive mentions in groups
	if (chatType !== 'private') {
		const mentions = content.match(/@\w+/g) || []
		if (mentions.length > 3) {
			result.warnings.push('提及用户过多')
			result.suggestions.push('在群组中减少 @ 提及的使用')
		}
	}

	// Check for bot commands
	const botCommands = content.match(/\/\w+/g) || []
	if (botCommands.length > 2) {
		result.warnings.push('包含多个机器人命令')
		result.suggestions.push('避免在一条消息中使用过多命令')
	}

	return result
}

/**
 * Get Telegram chat type description
 * @param {string} chatType - Chat type
 * @returns {string} Chat type description
 */
export function getChatTypeDescription(chatType) {
	switch (chatType) {
		case 'private':
			return '私聊 - 一对一的私人对话'
		case 'group':
			return '群组 - 小型群体聊天（最多200人）'
		case 'supergroup':
			return '超级群组 - 大型群体聊天（最多20万人）'
		case 'channel':
			return '频道 - 广播频道，用于发布信息'
		default:
			return '未知聊天类型'
	}
}
