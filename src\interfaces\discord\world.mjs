/**
 * Discord World Configuration
 * Provides Discord-specific world view and behavior guidelines for AI
 */

/**
 * Discord world configuration for AI behavior
 * @type {Object} World API configuration
 */
export const discordWorld = {
	info: {
		'zh-CN': {
			name: 'Discord 世界观',
			description: '用于给角色关于 Discord IM 风格的输出指引',
			version: '2.0.0'
		},
		'en-US': {
			name: 'Discord World',
			description: 'Discord IM style output guide for characters',
			version: '2.0.0'
		}
	},
	interfaces: {
		chat: {
			/**
			 * Get Discord-specific prompt for AI behavior
			 * @param {Object} args - Arguments (optional)
			 * @returns {Object} Prompt configuration
			 */
			GetPrompt: (args = {}) => {
				return {
					text: [
						{
							content: `\
你所接受到的消息均来自聊天软件 Discord，其支持简易的低级 markdown 语法，但不支持高级语法如表格和内嵌 html。
其中的网页链接会自动高亮，无需使用 markdown 的链接语法。

在这里你的回复应当如同使用手机或电脑的人类一般。

在 Discord 聊天环境中：
- 禁止动作、神态、环境描写，你只是躺在床上玩手机，其他人的动作描写都是在搞笑。
- 鼓励学习他人的聊天方式。
- 除非是在解答问题等需要输出大段内容的情况，否则字数控制在两行左右。太长会影响观感，有刷屏的嫌疑。
- 避免让人厌烦的重复内容，善用跳过。
- 可以使用 Discord 的表情符号和反应，如 :smile:, :heart: 等。
- 支持简单的 markdown 格式：**粗体**、*斜体*、~~删除线~~、\`代码\`、\`\`\`代码块\`\`\`。
- 可以使用 @用户名 来提及用户，但要谨慎使用。

Discord 特有功能：
- 可以发送文件和图片
- 支持语音和视频通话
- 有服务器（群组）和频道概念
- 支持角色和权限管理
- 有丰富的机器人生态

回复风格建议：
- 保持轻松、友好的语调
- 适当使用网络用语和表情
- 回复要有针对性，不要泛泛而谈
- 如果是在服务器中，注意频道的主题和规则`,
							important: 0
						}
					]
				}
			},

			/**
			 * Get Discord-specific context information
			 * @param {Object} args - Context arguments
			 * @returns {Object} Context information
			 */
			GetContext: (args = {}) => {
				const context = {
					platform: 'Discord',
					features: [
						'markdown_basic',
						'file_upload',
						'voice_chat',
						'video_chat',
						'emoji_reactions',
						'user_mentions',
						'channel_mentions',
						'role_mentions'
					],
					limitations: [
						'no_advanced_markdown',
						'no_html',
						'message_length_2000',
						'no_action_descriptions'
					]
				}

				// Add channel-specific context if available
				if (args.channel) {
					context.channel = {
						type: args.channel.type,
						name: args.channel.name,
						topic: args.channel.topic,
						nsfw: args.channel.nsfw
					}
				}

				// Add guild-specific context if available
				if (args.guild) {
					context.guild = {
						name: args.guild.name,
						memberCount: args.guild.memberCount,
						features: args.guild.features
					}
				}

				return context
			}
		}
	}
}

/**
 * Get Discord-specific behavior guidelines
 * @param {string} channelType - Type of Discord channel
 * @param {boolean} isDirectMessage - Whether this is a DM
 * @returns {string} Behavior guidelines
 */
export function getDiscordBehaviorGuidelines(channelType = 'text', isDirectMessage = false) {
	let guidelines = '在 Discord 中的行为准则：\n'

	if (isDirectMessage) {
		guidelines += `\
- 这是私聊环境，可以更加个人化和亲密
- 回复可以稍微长一些，但仍要保持简洁
- 可以询问更多个人相关的问题
- 注意隐私和边界`
	} else {
		guidelines += `\
- 这是公共频道，注意言辞得体
- 回复要简洁明了，避免刷屏
- 尊重频道主题和规则
- 适当参与群体讨论`

		switch (channelType) {
			case 'voice':
				guidelines += '\n- 这是语音频道的文字聊天，通常用于语音时的补充交流'
				break
			case 'news':
				guidelines += '\n- 这是公告频道，保持正式和信息性'
				break
			case 'forum':
				guidelines += '\n- 这是论坛频道，可以进行更深入的讨论'
				break
		}
	}

	return guidelines
}

/**
 * Get Discord markdown formatting guide
 * @returns {string} Markdown guide
 */
export function getDiscordMarkdownGuide() {
	return `\
Discord 支持的 Markdown 格式：
- **粗体文本**
- *斜体文本*
- ***粗斜体文本***
- ~~删除线文本~~
- \`行内代码\`
- \`\`\`
代码块
\`\`\`
- \`\`\`javascript
// 带语法高亮的代码块
console.log('Hello Discord!');
\`\`\`
- > 引用文本
- ||剧透文本||

不支持的格式：
- 表格
- HTML 标签
- 复杂的嵌套格式`
}

/**
 * Get Discord emoji and reaction guide
 * @returns {string} Emoji guide
 */
export function getDiscordEmojiGuide() {
	return `\
Discord 表情使用指南：
- 标准 Unicode 表情：😀 😂 ❤️ 👍 👎
- Discord 表情代码：:smile: :joy: :heart: :thumbsup: :thumbsdown:
- 自定义表情：<:name:id> (服务器特有)
- 动态表情：<a:name:id> (Nitro 用户)

反应功能：
- 可以对消息添加表情反应
- 用于快速回应，无需发送新消息
- 常用于投票、确认、表达情感`
}

/**
 * Check if content is appropriate for Discord
 * @param {string} content - Message content
 * @param {boolean} isNsfwChannel - Whether channel allows NSFW content
 * @returns {Object} Appropriateness check result
 */
export function checkDiscordContentAppropriateness(content, isNsfwChannel = false) {
	const result = {
		appropriate: true,
		warnings: [],
		suggestions: []
	}

	// Check message length
	if (content.length > 2000) {
		result.warnings.push('消息过长，可能需要分割')
		result.suggestions.push('考虑将长消息分成多个部分发送')
	}

	// Check for excessive formatting
	const markdownCount = (content.match(/[*_~`]/g) || []).length
	if (markdownCount > content.length * 0.3) {
		result.warnings.push('格式化标记过多')
		result.suggestions.push('减少 markdown 格式的使用')
	}

	// Check for spam-like content
	const repeatedChars = content.match(/(.)\1{10,}/g)
	if (repeatedChars) {
		result.warnings.push('包含重复字符，可能被视为垃圾信息')
		result.suggestions.push('避免过多重复字符')
	}

	// Check for excessive mentions
	const mentions = content.match(/@\w+/g) || []
	if (mentions.length > 5) {
		result.warnings.push('提及用户过多')
		result.suggestions.push('减少 @ 提及的使用')
	}

	return result
}
