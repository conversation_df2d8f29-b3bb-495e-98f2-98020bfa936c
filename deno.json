{"name": "gentian-aphrodite", "version": "2.0.0", "description": "An independent AI character companion powered by <PERSON><PERSON>", "author": "steve02081504", "license": "MIT", "tasks": {"start": "deno run --allow-all main.mjs", "dev": "deno run --allow-all --watch main.mjs", "discord": "deno run --allow-all main.mjs --platform discord", "telegram": "deno run --allow-all main.mjs --platform telegram", "cli": "deno run --allow-all main.mjs --platform cli", "test": "deno test --allow-all", "lint": "deno lint", "fmt": "deno fmt", "check": "deno check main.mjs"}, "imports": {"discord.js": "npm:discord.js@^14.14.1", "telegraf": "npm:telegraf@^4.15.0", "string-similarity": "npm:string-similarity@^4.0.4", "jieba": "npm:nodejieba@^2.6.0", "puppeteer": "npm:puppeteer-core@^24.9.0", "turndown": "npm:turndown@^7.1.2", "google-sr": "npm:google-sr@^3.0.3", "fast-xml-parser": "npm:fast-xml-parser@^4.3.4", "javascript-obfuscator": "npm:javascript-obfuscator@^4.0.2", "terser": "npm:terser@^5.26.0", "rollup": "npm:rollup@^4.9.6", "@nuintun/qrcode": "npm:@nuintun/qrcode@^3.3.2", "canvas": "https://deno.land/x/canvas@v1.4.1/mod.ts", "acorn": "npm:acorn@^8.11.0", "estree-walker": "npm:estree-walker@^3.0.3", "astring": "npm:astring@^1.8.6", "ast-types": "npm:ast-types@^0.16.1"}, "exclude": ["dist/", "data/", "logs/", "node_modules/"], "compilerOptions": {"allowJs": true, "checkJs": false, "strict": false, "allowUnreachableCode": false, "allowUnusedLabels": false, "noImplicitReturns": false, "noImplicitOverride": false, "noImplicitAny": false, "noImplicitThis": false, "noStrictGenericChecks": false}, "lint": {"rules": {"tags": ["recommended"], "exclude": ["no-unused-vars", "no-explicit-any"]}}, "fmt": {"useTabs": true, "lineWidth": 120, "indentWidth": 2, "semiColons": false, "singleQuote": true, "proseWrap": "preserve"}}