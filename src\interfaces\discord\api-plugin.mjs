/**
 * Discord API Plugin
 * Provides Discord-specific functionality for AI interactions
 */

/**
 * Keywords that might indicate Discord-related operations
 */
const DISCORD_OPERATION_KEYWORDS = [
	'身份组', '群', '频道', '设置', '服务器', 'ban', '踢了', '禁言',
	'管理', '操作', '权限', '置顶', '分区', '分组', '帖子', '表情', '贴纸',
	'修改', '封禁', '邀请', '话题', '投票', '动态', '匿名',
	'删了', '删掉', 'discord.com', 'guild', 'channel', 'role',
	'kick', 'mute', 'timeout', 'permission', 'moderate'
]

/**
 * Inappropriate content keywords (basic filtering)
 */
const INAPPROPRIATE_KEYWORDS = [
	'hack', 'exploit', 'spam', 'raid', 'nuke', 'token',
	'破解', '攻击', '刷屏', '轰炸', '令牌'
]

/**
 * Create Discord API plugin for AI interactions
 * @param {import('discord.js').Message} message - Discord message object
 * @param {import('discord.js').Client} client - Discord client
 * @returns {Object} Plugin API object
 */
export function createDiscordApiPlugin(message, client) {
	return {
		info: {
			'zh-CN': {
				name: 'Discord API 插件',
				description: 'Discord API 插件，让 AI 能够进行 Discord 相关操作',
				author: 'Gentian Aphrodite Team',
				version: '2.0.0'
			},
			'en-US': {
				name: 'Discord API Plugin',
				description: 'Discord API plugin for AI Discord operations',
				author: 'Gentian Aphrodite Team',
				version: '2.0.0'
			}
		},
		interfaces: {
			chat: {
				/**
				 * Get JavaScript code prompt for Discord operations
				 * @param {string} args - User input/request
				 * @param {any} result - Previous result
				 * @param {string} detailLevel - Detail level
				 * @returns {Promise<string>} Code prompt
				 */
				GetJSCodePrompt: async (args, result, detailLevel) => {
					// Check if request involves Discord operations
					const hasDiscordKeywords = DISCORD_OPERATION_KEYWORDS.some(keyword =>
						args.toLowerCase().includes(keyword.toLowerCase())
					)

					// Check for inappropriate requests
					const hasInappropriateContent = INAPPROPRIATE_KEYWORDS.some(keyword =>
						args.toLowerCase().includes(keyword.toLowerCase())
					)

					if (hasInappropriateContent) {
						return null // Don't provide code for inappropriate requests
					}

					if (hasDiscordKeywords) {
						return `\
你可以使用以下变量来访问 Discord API:
- message: 你正在回复的 Discord 消息对象
- channel: 发生回复的 Discord 频道对象
- guild: 发生回复的 Discord 服务器对象 (如果在服务器中)
- client: Discord.js 客户端实例

可用的操作示例:
- 获取频道信息: channel.name, channel.type, channel.topic
- 获取服务器信息: guild?.name, guild?.memberCount
- 获取用户信息: message.author.username, message.author.id
- 发送消息: channel.send("内容")
- 获取消息历史: channel.messages.fetch({limit: 10})

注意: 请谨慎使用管理功能，确保有适当的权限检查。`
					}

					return null
				},

				/**
				 * Get JavaScript code execution context
				 * @param {string} args - User input/request
				 * @param {any} result - Previous result
				 * @param {string} detailLevel - Detail level
				 * @returns {Promise<Object>} Execution context
				 */
				GetJSCodeContext: async (args, result, detailLevel) => {
					return {
						message,
						channel: message.channel,
						guild: message.guild,
						client: client,
						// Utility functions
						utils: {
							/**
							 * Safely get channel by ID
							 * @param {string} channelId - Channel ID
							 * @returns {Promise<any>} Channel object or null
							 */
							getChannel: async (channelId) => {
								try {
									return await client.channels.fetch(channelId)
								} catch (error) {
									console.warn('Failed to fetch channel:', error)
									return null
								}
							},

							/**
							 * Safely get user by ID
							 * @param {string} userId - User ID
							 * @returns {Promise<any>} User object or null
							 */
							getUser: async (userId) => {
								try {
									return await client.users.fetch(userId)
								} catch (error) {
									console.warn('Failed to fetch user:', error)
									return null
								}
							},

							/**
							 * Check if user has permission
							 * @param {string} userId - User ID
							 * @param {string} permission - Permission name
							 * @returns {boolean} True if user has permission
							 */
							hasPermission: (userId, permission) => {
								if (!message.guild) return false
								
								try {
									const member = message.guild.members.cache.get(userId)
									if (!member) return false
									
									return member.permissions.has(permission)
								} catch (error) {
									console.warn('Failed to check permission:', error)
									return false
								}
							},

							/**
							 * Check if user is owner or admin
							 * @param {string} userId - User ID
							 * @returns {boolean} True if user is owner/admin
							 */
							isOwnerOrAdmin: (userId) => {
								if (!message.guild) return false
								
								try {
									const member = message.guild.members.cache.get(userId)
									if (!member) return false
									
									return member.permissions.has('Administrator') || 
										   message.guild.ownerId === userId
								} catch (error) {
									console.warn('Failed to check admin status:', error)
									return false
								}
							},

							/**
							 * Format user mention
							 * @param {string} userId - User ID
							 * @returns {string} Formatted mention
							 */
							mentionUser: (userId) => `<@${userId}>`,

							/**
							 * Format channel mention
							 * @param {string} channelId - Channel ID
							 * @returns {string} Formatted mention
							 */
							mentionChannel: (channelId) => `<#${channelId}>`,

							/**
							 * Format role mention
							 * @param {string} roleId - Role ID
							 * @returns {string} Formatted mention
							 */
							mentionRole: (roleId) => `<@&${roleId}>`,

							/**
							 * Get current timestamp
							 * @returns {number} Current timestamp
							 */
							now: () => Date.now(),

							/**
							 * Sleep for specified milliseconds
							 * @param {number} ms - Milliseconds to sleep
							 * @returns {Promise<void>}
							 */
							sleep: (ms) => new Promise(resolve => setTimeout(resolve, ms))
						}
					}
				}
			}
		}
	}
}

/**
 * Check if message content suggests Discord API usage
 * @param {string} content - Message content
 * @returns {boolean} True if likely needs Discord API
 */
export function needsDiscordApi(content) {
	if (!content) return false

	const lowerContent = content.toLowerCase()
	
	return DISCORD_OPERATION_KEYWORDS.some(keyword =>
		lowerContent.includes(keyword.toLowerCase())
	)
}

/**
 * Check if request is appropriate for Discord API
 * @param {string} content - Message content
 * @returns {boolean} True if appropriate
 */
export function isAppropriateRequest(content) {
	if (!content) return true

	const lowerContent = content.toLowerCase()
	
	return !INAPPROPRIATE_KEYWORDS.some(keyword =>
		lowerContent.includes(keyword.toLowerCase())
	)
}

/**
 * Get Discord API capabilities description
 * @returns {string} Capabilities description
 */
export function getApiCapabilities() {
	return `\
Discord API 插件功能:
- 频道管理: 获取频道信息、发送消息、管理权限
- 用户管理: 获取用户信息、检查权限、管理成员
- 服务器管理: 获取服务器信息、管理角色、频道设置
- 消息处理: 发送、编辑、删除消息，处理反应
- 权限检查: 验证用户权限、管理员状态检查

注意事项:
- 所有操作都需要适当的权限
- 不支持破坏性或恶意操作
- 遵循 Discord 服务条款和社区准则`
}
