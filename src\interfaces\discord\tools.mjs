/**
 * Discord Platform Tools
 * Utility functions for Discord message processing and formatting
 */

/**
 * Split Discord message content for character limit
 * @param {string} reply - Message content to split
 * @param {number} splitLength - Maximum length per message (default: 2000)
 * @returns {string[]} Array of split message parts
 */
export function splitDiscordReply(reply, splitLength = 2000) {
	if (!reply || reply.length <= splitLength) {
		return [reply || '']
	}

	let contentSlices = reply.split('\n')
	let newContentSlices = []
	let last = ''

	function mapend() {
		if (last) newContentSlices.push(last)
		contentSlices = newContentSlices
		newContentSlices = []
		last = ''
	}

	// First pass: combine short lines
	for (const contentSlice of contentSlices) {
		if (last.length + contentSlice.length + 1 <= splitLength) {
			last += (last ? '\n' : '') + contentSlice
		} else {
			newContentSlices.push(last)
			last = contentSlice
		}
	}

	mapend()

	// Second pass: handle code blocks and long lines
	for (const contentSlice of contentSlices) {
		if (contentSlice.length > splitLength) {
			if (contentSlice.startsWith('```')) {
				// Try to split code blocks intelligently
				const splitters = ['}', '};', ')', '']
				let handled = false

				for (const splitter of splitters) {
					const splitBlocks = splitCodeBlock(contentSlice, splitter)
					if (splitBlocks.every(block => block.length <= splitLength)) {
						newContentSlices.push(...splitBlocks)
						handled = true
						break
					}
				}

				if (!handled) {
					newContentSlices.push(contentSlice)
				}
			} else {
				// Split long non-code lines
				const splitLines = contentSlice.split(/(?<=[ !"');?\]}'"。》！）：；？])/)
				let current = ''

				for (const splitLine of splitLines) {
					if (current.length + splitLine.length > splitLength) {
						if (current) newContentSlices.push(current)
						current = splitLine
					} else {
						current += splitLine
					}
				}

				if (current) newContentSlices.push(current)
			}
		} else {
			newContentSlices.push(contentSlice)
		}
	}

	mapend()

	// Third pass: force split anything still too long
	for (const contentSlice of contentSlices) {
		if (contentSlice.length > splitLength) {
			const chunks = contentSlice.match(new RegExp(`.{1,${splitLength}}`, 'g')) || []
			newContentSlices.push(...chunks)
		} else {
			newContentSlices.push(contentSlice)
		}
	}

	mapend()

	// Final pass: combine messages to maximize length
	for (const contentSlice of contentSlices) {
		if (last.length + contentSlice.length + 1 < splitLength) {
			last += (last ? '\n' : '') + contentSlice
		} else {
			if (last) newContentSlices.push(last)
			last = contentSlice
		}
	}

	mapend()

	return contentSlices.map(slice => slice.trim()).filter(slice => slice)
}

/**
 * Split code block content intelligently
 * @param {string} codeBlock - Code block content
 * @param {string} splitter - Character to split on
 * @returns {string[]} Split code blocks
 */
function splitCodeBlock(codeBlock, splitter) {
	if (!splitter) {
		return [codeBlock]
	}

	const lines = codeBlock.split('\n')
	const language = lines[0].replace('```', '')
	const codeContent = lines.slice(1, -1).join('\n')
	const parts = codeContent.split(splitter)

	if (parts.length <= 1) {
		return [codeBlock]
	}

	const result = []
	for (let i = 0; i < parts.length; i++) {
		const part = parts[i] + (i < parts.length - 1 ? splitter : '')
		result.push(`\`\`\`${language}\n${part}\n\`\`\``)
	}

	return result
}

/**
 * Format Discord embed content
 * @param {Object} embed - Discord embed object
 * @returns {string} Formatted embed text
 */
function formatEmbed(embed) {
	let embedText = ''

	if (embed.title) {
		embedText += `**${embed.title}**\n`
	}

	if (embed.description) {
		embedText += `${embed.description}\n`
	}

	if (embed.fields && embed.fields.length > 0) {
		for (const field of embed.fields) {
			embedText += `**${field.name}**: ${field.value}\n`
		}
	}

	if (embed.footer) {
		embedText += `*${embed.footer.text}*\n`
	}

	return embedText.trim()
}

/**
 * Format Discord message content with mentions, embeds, and attachments
 * @param {Object} message - Discord message object
 * @returns {string} Formatted message content
 */
function formatMessageContent(message) {
	let content = message.content || ''

	// Process user mentions
	for (const [_, user] of message.mentions?.users || new Map()) {
		const mentionTag = `<@${user.id}>`
		const mentionTagNick = `<@!${user.id}>`
		content = content.replaceAll(mentionTag, `@${user.username}`)
		content = content.replaceAll(mentionTagNick, `@${user.username}`)
	}

	// Process channel mentions
	for (const [_, channel] of message.mentions?.channels || new Map()) {
		const mentionTag = `<#${channel.id}>`
		content = content.replaceAll(mentionTag, `#${channel.name}`)
	}

	// Process role mentions
	for (const [_, role] of message.mentions?.roles || new Map()) {
		const mentionTag = `<@&${role.id}>`
		content = content.replaceAll(mentionTag, `@${role.name}`)
	}

	// Add embeds
	for (const embed of message.embeds || []) {
		const embedText = formatEmbed(embed)
		if (embedText) {
			if (content) content += '\n'
			content += embedText
		}
	}

	// Add attachment information
	for (const attachment of message.attachments || []) {
		if (attachment.url) {
			if (content) content += '\n'
			content += `[附件] ${attachment.name || 'Unknown'}: ${attachment.url}`
		}
	}

	// Mark if edited
	if (message.editedTimestamp) {
		content += ' (已编辑)'
	}

	return content
}

/**
 * Get full message content including referenced messages
 * @param {Object} message - Discord message object
 * @param {Object} client - Discord client instance
 * @returns {Promise<string>} Full message content
 */
export async function getMessageFullContent(message, client) {
	let fullContent = formatMessageContent(message)

	// Handle message references (replies)
	if (message.reference && message.reference.messageId) {
		try {
			const referencedMessage = await message.channel.messages.fetch(message.reference.messageId)
			if (referencedMessage) {
				const refContent = formatMessageContent(referencedMessage)
				const authorName = referencedMessage.author?.username || '未知用户'
				if (fullContent) fullContent += '\n\n'
				fullContent += `（回复消息）\n${authorName}：${refContent}`
			}
		} catch (error) {
			console.warn('Failed to fetch referenced message:', error)
		}
	}

	// Handle message snapshots (forwarded messages)
	const referencedMessages = message.messageSnapshots?.map(snapshot => snapshot) || []
	for (const referencedMessage of referencedMessages) {
		const refContent = formatMessageContent(referencedMessage)
		const authorName = referencedMessage.author?.username || '未知用户'
		if (fullContent) fullContent += '\n\n'
		fullContent += `（转发消息）\n${authorName}：${refContent}`
	}

	return fullContent
}

/**
 * Check if message content contains specific keywords
 * @param {string} content - Message content
 * @param {string[]} keywords - Keywords to check
 * @param {string} mode - Matching mode ('any', 'all', 'exact')
 * @returns {boolean} True if keywords match
 */
export function containsKeywords(content, keywords, mode = 'any') {
	if (!content || !keywords || keywords.length === 0) {
		return false
	}

	const lowerContent = content.toLowerCase()
	const lowerKeywords = keywords.map(keyword => keyword.toLowerCase())

	switch (mode) {
		case 'all':
			return lowerKeywords.every(keyword => lowerContent.includes(keyword))
		case 'exact':
			return lowerKeywords.some(keyword => lowerContent === keyword)
		case 'any':
		default:
			return lowerKeywords.some(keyword => lowerContent.includes(keyword))
	}
}

/**
 * Extract Discord snowflake ID from various formats
 * @param {string} input - Input string that might contain an ID
 * @returns {string|null} Extracted ID or null
 */
export function extractDiscordId(input) {
	if (!input) return null

	// Direct ID (18-19 digits)
	const directMatch = input.match(/^\d{17,19}$/)
	if (directMatch) return directMatch[0]

	// Mention format <@123456789>
	const mentionMatch = input.match(/<@!?(\d{17,19})>/)
	if (mentionMatch) return mentionMatch[1]

	// Channel format <#123456789>
	const channelMatch = input.match(/<#(\d{17,19})>/)
	if (channelMatch) return channelMatch[1]

	// Role format <@&123456789>
	const roleMatch = input.match(/<@&(\d{17,19})>/)
	if (roleMatch) return roleMatch[1]

	return null
}
