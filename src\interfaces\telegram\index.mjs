/**
 * Telegram Platform Interface
 * Provides Telegram integration for Gentian Aphrodite
 * Independent implementation replacing fount platform dependencies
 */

import { Telegraf } from 'telegraf'
import { chatManager } from '../../core/chat-manager.mjs'
import { aiManager } from '../../core/ai-manager.mjs'
import { splitTelegramReply, aiMarkdownToTelegramHtml, telegramEntitiesToAiMarkdown } from './tools.mjs'
import { createTelegramApiPlugin, needsTelegramApi } from './api-plugin.mjs'
import { telegramWorld, getTelegramBehaviorGuidelines } from './world.mjs'

/**
 * @typedef {import('../../types/index.mjs').TelegramConfig} TelegramConfig
 * @typedef {import('../../types/index.mjs').PlatformInterface} PlatformInterface
 * @typedef {import('../../types/index.mjs').ChatMessage} ChatMessage
 */

/**
 * Telegram Platform implementation
 * @implements {PlatformInterface}
 */
export class TelegramPlatform {
	/**
	 * @param {TelegramConfig} config - Telegram configuration
	 * @param {Object} app - Application instance
	 */
	constructor(config, app) {
		this.config = config
		this.app = app
		this.name = 'Telegram'
		this.bot = null
		this.isRunning = false
		this.botInfo = null
		this.userCache = new Map()
		this.chatCache = new Map()

		// Telegram interface configuration
		this.interfaceConfig = {
			OwnerUserID: app.config?.character?.owner?.telegramId || '',
			OwnerUserName: app.config?.character?.owner?.username || 'owner',
			OwnerNameKeywords: app.config?.character?.owner?.keywords || ['主人', 'owner']
		}
	}

	/**
	 * Start the Telegram platform
	 * @returns {Promise<void>}
	 */
	async start() {
		if (this.isRunning) {
			console.log('⚠️ Telegram platform is already running')
			return
		}

		console.log('🚀 Starting Telegram platform...')

		try {
			// Validate configuration
			if (!this.config.token) {
				throw new Error('Telegram bot token is required. Please set TELEGRAM_BOT_TOKEN in your .env file.')
			}

			// Initialize Telegram bot
			await this.initializeBot()

			// Connect to Telegram
			await this.connect()

			console.log('✅ Telegram platform started successfully')
			this.isRunning = true

		} catch (error) {
			console.error('❌ Failed to start Telegram platform:', error)
			throw error
		}
	}

	/**
	 * Initialize Telegram bot with proper configuration
	 * @private
	 * @returns {Promise<void>}
	 */
	async initializeBot() {
		console.log('🔧 Initializing Telegram bot...')

		// Create Telegraf instance
		this.bot = new Telegraf(this.config.token)

		// Set up event handlers
		this.setupEventHandlers()

		// Get bot information
		try {
			this.botInfo = await this.bot.telegram.getMe()
			console.log(`🤖 Bot info: @${this.botInfo.username} (${this.botInfo.first_name})`)
		} catch (error) {
			console.error('Failed to get bot info:', error)
			throw new Error('Failed to get bot information from Telegram')
		}

		console.log('✅ Telegram bot initialized')
	}

	/**
	 * Connect to Telegram
	 * @private
	 * @returns {Promise<void>}
	 */
	async connect() {
		console.log('🔌 Connecting to Telegram...')

		try {
			// Start polling for updates
			await this.bot.launch()
			console.log(`✅ Connected to Telegram as @${this.botInfo.username}`)

		} catch (error) {
			console.error('Failed to connect to Telegram:', error)
			throw error
		}
	}

	/**
	 * Set up Telegram event handlers
	 * @private
	 * @returns {void}
	 */
	setupEventHandlers() {
		// Handle text messages
		this.bot.on('text', async (ctx) => {
			await this.handleMessage(ctx)
		})

		// Handle photo messages
		this.bot.on('photo', async (ctx) => {
			await this.handleMessage(ctx)
		})

		// Handle document messages
		this.bot.on('document', async (ctx) => {
			await this.handleMessage(ctx)
		})

		// Handle video messages
		this.bot.on('video', async (ctx) => {
			await this.handleMessage(ctx)
		})

		// Handle audio messages
		this.bot.on('audio', async (ctx) => {
			await this.handleMessage(ctx)
		})

		// Handle voice messages
		this.bot.on('voice', async (ctx) => {
			await this.handleMessage(ctx)
		})

		// Handle sticker messages
		this.bot.on('sticker', async (ctx) => {
			await this.handleMessage(ctx)
		})

		// Handle edited messages
		this.bot.on('edited_message', async (ctx) => {
			await this.handleMessageUpdate(ctx)
		})

		// Handle errors
		this.bot.catch((error, ctx) => {
			console.error('Telegram bot error:', error)
			console.error('Context:', ctx)
		})

		console.log('📡 Telegram event handlers set up')
	}

	/**
	 * Handle incoming Telegram message
	 * @private
	 * @param {Object} ctx - Telegraf context
	 * @returns {Promise<void>}
	 */
	async handleMessage(ctx) {
		try {
			// Skip messages from bots (except our own)
			if (ctx.from.is_bot && ctx.from.id !== this.botInfo.id) return

			// Convert Telegram message to internal format
			const chatMessage = await this.convertTelegramMessage(ctx)
			if (!chatMessage) return

			// Add to chat history
			const chatId = this.getChatId(ctx)
			await chatManager.addMessage(chatId, chatMessage)

			// Check if bot should respond
			const shouldRespond = this.shouldRespondToMessage(ctx, chatMessage)
			if (!shouldRespond) return

			// Send typing indicator
			await this.sendTyping(chatId)

			// Generate AI response
			const response = await this.generateResponse(chatId, chatMessage)
			if (response) {
				await this.sendMessage(chatId, response)
			}

		} catch (error) {
			console.error('Error handling Telegram message:', error)
		}
	}

	/**
	 * Handle Telegram message update
	 * @private
	 * @param {Object} ctx - Telegraf context
	 * @returns {Promise<void>}
	 */
	async handleMessageUpdate(ctx) {
		try {
			// Convert updated message to internal format
			const chatMessage = await this.convertTelegramMessage(ctx)
			if (!chatMessage) return

			// Update in chat history
			const chatId = this.getChatId(ctx)
			await chatManager.updateMessage(chatId, ctx.message.message_id.toString(), chatMessage)

		} catch (error) {
			console.error('Error handling Telegram message update:', error)
		}
	}

	/**
	 * Get chat ID from Telegram context
	 * @private
	 * @param {Object} ctx - Telegraf context
	 * @returns {string} Chat ID
	 */
	getChatId(ctx) {
		const chatId = ctx.chat.id.toString()

		// Handle thread messages (topics in supergroups)
		if (ctx.message.message_thread_id) {
			return `${chatId}_${ctx.message.message_thread_id}`
		}

		return chatId
	}

	/**
	 * Convert Telegram message to internal ChatMessage format
	 * @private
	 * @param {Object} ctx - Telegraf context
	 * @returns {Promise<ChatMessage|null>} Converted message or null if invalid
	 */
	async convertTelegramMessage(ctx) {
		try {
			const message = ctx.message || ctx.editedMessage
			if (!message || !message.from) return null

			// Cache user information
			this.userCache.set(message.from.id, {
				id: message.from.id,
				username: message.from.username,
				firstName: message.from.first_name,
				lastName: message.from.last_name,
				displayName: this.getUserDisplayName(message.from)
			})

			// Cache chat information
			this.chatCache.set(message.chat.id, {
				id: message.chat.id,
				type: message.chat.type,
				title: message.chat.title,
				username: message.chat.username
			})

			// Determine if message is from owner
			const isFromOwner = this.isOwnerMessage(message)

			// Process message content
			let content = ''
			const files = []

			// Handle text content using enhanced tools
			if (message.text || message.caption) {
				const text = message.text || message.caption
				const entities = message.entities || message.caption_entities
				content = telegramEntitiesToAiMarkdown(text, entities, this.botInfo, message.reply_to_message)
			}

			// Handle different message types
			if (message.photo) {
				const photo = message.photo[message.photo.length - 1] // Get highest resolution
				files.push({
					name: 'photo.jpg',
					url: await this.getFileUrl(photo.file_id),
					size: photo.file_size,
					contentType: 'image/jpeg',
					telegram_file_id: photo.file_id
				})
			}

			if (message.document) {
				files.push({
					name: message.document.file_name || 'document',
					url: await this.getFileUrl(message.document.file_id),
					size: message.document.file_size,
					contentType: message.document.mime_type,
					telegram_file_id: message.document.file_id
				})
			}

			if (message.video) {
				files.push({
					name: 'video.mp4',
					url: await this.getFileUrl(message.video.file_id),
					size: message.video.file_size,
					contentType: 'video/mp4',
					telegram_file_id: message.video.file_id
				})
			}

			if (message.audio) {
				files.push({
					name: message.audio.file_name || 'audio.mp3',
					url: await this.getFileUrl(message.audio.file_id),
					size: message.audio.file_size,
					contentType: message.audio.mime_type || 'audio/mpeg',
					telegram_file_id: message.audio.file_id
				})
			}

			if (message.voice) {
				files.push({
					name: 'voice.ogg',
					url: await this.getFileUrl(message.voice.file_id),
					size: message.voice.file_size,
					contentType: 'audio/ogg',
					telegram_file_id: message.voice.file_id
				})
			}

			if (message.sticker) {
				content += `[贴纸: ${message.sticker.emoji || '🎭'}]`
				if (message.sticker.is_animated) {
					content += ' (动态)'
				}
			}

			// Handle reply to message
			if (message.reply_to_message) {
				const replyToUser = this.getUserDisplayName(message.reply_to_message.from)
				const replyToContent = message.reply_to_message.text || message.reply_to_message.caption || '[媒体消息]'
				content = `> ${replyToUser}: ${replyToContent.substring(0, 100)}${replyToContent.length > 100 ? '...' : ''}\n\n${content}`
			}

			return {
				id: message.message_id.toString(),
				content: content.trim(),
				role: isFromOwner ? 'user' : 'char',
				name: this.getUserDisplayName(message.from),
				timestamp: message.date * 1000, // Convert to milliseconds
				files,
				extension: {
					platform: 'telegram',
					platform_message_id: message.message_id,
					platform_chat_id: message.chat.id,
					platform_user_id: message.from.id,
					platform_chat_type: message.chat.type,
					platform_chat_title: message.chat.title,
					is_direct_message: message.chat.type === 'private',
					is_from_owner: isFromOwner,
					mentions_bot: this.mentionsBot(message),
					mentions_owner: this.mentionsOwner(message),
					telegram_message_obj: message,
					telegram_context: ctx
				}
			}

		} catch (error) {
			console.error('Error converting Telegram message:', error)
			return null
		}
	}

	/**
	 * Get bot ID (Telegram implementation)
	 * @returns {string} Bot ID
	 */
	getBotId() {
		return this.botInfo?.id?.toString() || 'unknown'
	}

	/**
	 * Get bot name (Telegram implementation)
	 * @returns {string} Bot name
	 */
	getBotName() {
		return this.botInfo?.username || this.app.config.character.info.name
	}

	/**
	 * Get user display name
	 * @private
	 * @param {Object} user - Telegram user object
	 * @returns {string} Display name
	 */
	getUserDisplayName(user) {
		if (!user) return '未知用户'

		if (user.first_name && user.last_name) {
			return `${user.first_name} ${user.last_name}`
		} else if (user.first_name) {
			return user.first_name
		} else if (user.username) {
			return `@${user.username}`
		} else {
			return `User_${user.id}`
		}
	}

	/**
	 * Check if message is from owner
	 * @private
	 * @param {Object} message - Telegram message
	 * @returns {boolean} True if from owner
	 */
	isOwnerMessage(message) {
		const userId = message.from.id.toString()
		const username = message.from.username || ''

		return userId === this.interfaceConfig.OwnerUserID ||
			   username === this.interfaceConfig.OwnerUserName ||
			   this.interfaceConfig.OwnerNameKeywords.some(keyword =>
				   username.toLowerCase().includes(keyword.toLowerCase())
			   )
	}

	/**
	 * Check if message mentions bot
	 * @private
	 * @param {Object} message - Telegram message
	 * @returns {boolean} True if mentions bot
	 */
	mentionsBot(message) {
		if (!message.entities) return false

		return message.entities.some(entity => {
			if (entity.type === 'mention') {
				const mention = message.text.substring(entity.offset, entity.offset + entity.length)
				return mention === `@${this.botInfo.username}`
			}
			return false
		})
	}

	/**
	 * Check if message mentions owner
	 * @private
	 * @param {Object} message - Telegram message
	 * @returns {boolean} True if mentions owner
	 */
	mentionsOwner(message) {
		if (!message.entities) return false

		return message.entities.some(entity => {
			if (entity.type === 'mention') {
				const mention = message.text.substring(entity.offset, entity.offset + entity.length)
				return mention === `@${this.interfaceConfig.OwnerUserName}`
			}
			return false
		})
	}

	/**
	 * Get file URL from Telegram file ID
	 * @private
	 * @param {string} fileId - Telegram file ID
	 * @returns {Promise<string>} File URL
	 */
	async getFileUrl(fileId) {
		try {
			const file = await this.bot.telegram.getFile(fileId)
			return `https://api.telegram.org/file/bot${this.config.token}/${file.file_path}`
		} catch (error) {
			console.error('Failed to get file URL:', error)
			return ''
		}
	}

	/**
	 * Check if bot should respond to message
	 * @private
	 * @param {Object} ctx - Telegraf context
	 * @param {ChatMessage} chatMessage - Converted chat message
	 * @returns {boolean} True if should respond
	 */
	shouldRespondToMessage(ctx, chatMessage) {
		// Always respond to private messages
		if (chatMessage.extension.is_direct_message) {
			return true
		}

		// Respond if bot is mentioned
		if (chatMessage.extension.mentions_bot) {
			return true
		}

		// Respond if owner is mentioned and message is from owner
		if (chatMessage.extension.is_from_owner && chatMessage.extension.mentions_owner) {
			return true
		}

		// Random chance to respond in groups (configurable)
		const randomChance = 0.1 // 10% chance
		return Math.random() < randomChance
	}

	/**
	 * Generate AI response for message
	 * @private
	 * @param {string} chatId - Chat ID
	 * @param {ChatMessage} message - Trigger message
	 * @returns {Promise<Object|null>} Response object or null
	 */
	async generateResponse(chatId, message) {
		try {
			// Get chat history
			const history = await chatManager.getHistory(chatId, 20)

			// Build prompt
			const prompt = this.buildPrompt(history, message)

			// Call AI
			const response = await aiManager.call('chat', prompt)

			if (response && response.content) {
				return {
					content: response.content,
					files: [],
					extension: {
						source: response.source,
						platform: 'telegram'
					}
				}
			}

			return null

		} catch (error) {
			console.error('Error generating AI response:', error)
			return null
		}
	}

	/**
	 * Build prompt for AI with Telegram context
	 * @private
	 * @param {ChatMessage[]} history - Chat history
	 * @param {ChatMessage} triggerMessage - Trigger message
	 * @returns {string} AI prompt
	 */
	buildPrompt(history, triggerMessage) {
		const config = this.app.config
		const characterName = config.character.info.name
		const characterDescription = config.character.info.description || ''

		// Get Telegram world guidelines
		const worldPrompt = telegramWorld.interfaces.chat.GetPrompt()
		const telegramGuidelines = worldPrompt.text[0].content

		// Get chat-specific behavior guidelines
		const isDirectMessage = triggerMessage.extension?.is_direct_message || false
		const chatType = triggerMessage.extension?.platform_chat_type || 'private'
		const behaviorGuidelines = getTelegramBehaviorGuidelines(chatType, isDirectMessage)

		let prompt = `你是 ${characterName}。${characterDescription}\n\n`
		prompt += `${telegramGuidelines}\n\n`
		prompt += `${behaviorGuidelines}\n\n`
		prompt += '聊天历史:\n'

		// Add recent history
		for (const msg of history.slice(-10)) {
			const role = msg.role === 'user' ? msg.name : characterName
			prompt += `${role}: ${msg.content}\n`
		}

		// Add context about Telegram features if needed
		if (needsTelegramApi(triggerMessage.content)) {
			prompt += '\n注意：用户的请求可能涉及 Telegram 功能，你可以参考 Telegram API 插件的功能。'
		}

		prompt += `\n请以 ${characterName} 的身份回复最后一条消息。保持角色一致性，回复要自然、有趣，符合 Telegram 聊天环境。`

		return prompt
	}

	/**
	 * Send message (Telegram implementation)
	 * @param {string} channelId - Telegram chat ID
	 * @param {Object} reply - Reply object
	 * @returns {Promise<void>}
	 */
	async sendMessage(channelId, reply) {
		try {
			const { chatId, threadId } = this.parseChannelId(channelId)

			// Convert AI markdown to Telegram HTML and split long messages
			const htmlContent = aiMarkdownToTelegramHtml(reply.content || '')
			const messages = splitTelegramReply(htmlContent, 4096)

			for (const messageContent of messages) {
				if (messageContent.trim()) {
					const sendOptions = {
						parse_mode: 'HTML',
						...(threadId && { message_thread_id: threadId })
					}

					// Send text message
					const sentMessage = await this.bot.telegram.sendMessage(
						chatId,
						messageContent,
						sendOptions
					)

					// Add sent message to chat history
					const chatMessage = await this.convertTelegramMessage({
						message: sentMessage,
						chat: { id: chatId }
					})
					if (chatMessage) {
						await chatManager.addMessage(channelId, chatMessage)
					}
				}
			}

			// Send files if any
			if (reply.files && reply.files.length > 0) {
				for (const file of reply.files) {
					await this.sendFile(chatId, file, threadId)
				}
			}

		} catch (error) {
			console.error('Error sending Telegram message:', error)
			throw error
		}
	}

	/**
	 * Send typing indicator (Telegram implementation)
	 * @param {string} channelId - Telegram chat ID
	 * @returns {Promise<void>}
	 */
	async sendTyping(channelId) {
		try {
			const { chatId } = this.parseChannelId(channelId)
			await this.bot.telegram.sendChatAction(chatId, 'typing')
		} catch (error) {
			console.error('Error sending typing indicator:', error)
		}
	}

	/**
	 * Get chat history (Telegram implementation)
	 * @param {string} channelId - Telegram chat ID
	 * @param {number} limit - Message limit
	 * @returns {Promise<ChatMessage[]>} Chat history
	 */
	async getHistory(channelId, limit) {
		try {
			// First try to get from chat manager
			const history = await chatManager.getHistory(channelId, limit)
			if (history && history.length > 0) {
				return history
			}

			// If no history in chat manager, we can't fetch from Telegram
			// because Telegram doesn't provide a direct way to get message history
			// We can only process messages as they come in
			return []

		} catch (error) {
			console.error('Error getting Telegram history:', error)
			return []
		}
	}

	/**
	 * Parse channel ID to extract chat ID and thread ID
	 * @private
	 * @param {string} channelId - Channel ID
	 * @returns {Object} Parsed IDs
	 */
	parseChannelId(channelId) {
		if (channelId.includes('_')) {
			const parts = channelId.split('_')
			return {
				chatId: parseInt(parts[0], 10),
				threadId: parseInt(parts[1], 10)
			}
		}
		return { chatId: parseInt(channelId, 10) }
	}



	/**
	 * Send file to Telegram
	 * @private
	 * @param {number} chatId - Chat ID
	 * @param {Object} file - File object
	 * @param {number} [threadId] - Thread ID
	 * @returns {Promise<void>}
	 */
	async sendFile(chatId, file, threadId) {
		try {
			const sendOptions = {
				caption: file.caption || '',
				...(threadId && { message_thread_id: threadId })
			}

			if (file.contentType?.startsWith('image/')) {
				await this.bot.telegram.sendPhoto(chatId, file.url, sendOptions)
			} else if (file.contentType?.startsWith('video/')) {
				await this.bot.telegram.sendVideo(chatId, file.url, sendOptions)
			} else if (file.contentType?.startsWith('audio/')) {
				await this.bot.telegram.sendAudio(chatId, file.url, sendOptions)
			} else {
				await this.bot.telegram.sendDocument(chatId, file.url, sendOptions)
			}
		} catch (error) {
			console.error('Error sending file:', error)
		}
	}

	/**
	 * Cleanup platform (Telegram implementation)
	 * @returns {Promise<void>}
	 */
	async destroy() {
		if (this.bot) {
			await this.bot.stop()
		}
		this.isRunning = false
		console.log('🔌 Telegram Platform shutdown')
	}
}
