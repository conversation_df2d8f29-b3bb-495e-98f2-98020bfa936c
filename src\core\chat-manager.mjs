/**
 * Chat management system
 * Independent implementation replacing fount platform dependencies
 */

import { DataManager } from './data-manager.mjs'
import { configManager } from './config.mjs'

/**
 * @typedef {import('../types/index.mjs').ChatMessage} ChatMessage
 * @typedef {import('../types/index.mjs').ChatRequest} ChatRequest
 * @typedef {import('../types/index.mjs').ChatReply} ChatReply
 */

/**
 * Chat manager for handling chat history and message processing
 */
export class ChatManager {
	constructor() {
		this.chatHistories = new Map()
		this.maxHistoryLength = 100
		this.initialized = false
	}

	/**
	 * Initialize chat manager
	 * @returns {Promise<void>}
	 */
	async initialize() {
		if (this.initialized) return

		const config = configManager.get()
		if (config?.character?.memory?.maxEntries) {
			this.maxHistoryLength = config.character.memory.maxEntries
		}

		// Load existing chat histories
		await this.loadChatHistories()

		this.initialized = true
		console.log('Chat Manager initialized')
	}

	/**
	 * Load chat histories from storage
	 * @private
	 * @returns {Promise<void>}
	 */
	async loadChatHistories() {
		try {
			const files = await DataManager.listFiles('data/chats', '.json')
			
			for (const file of files) {
				try {
					const chatId = file.replace('data/chats/', '').replace('.json', '')
					const history = await DataManager.load(file, [])
					this.chatHistories.set(chatId, history)
				} catch (error) {
					console.warn(`Failed to load chat history from ${file}:`, error)
				}
			}

			console.log(`Loaded ${this.chatHistories.size} chat histories`)
		} catch (error) {
			console.warn('Failed to load chat histories:', error)
		}
	}

	/**
	 * Save chat history to storage
	 * @private
	 * @param {string} chatId - Chat identifier
	 * @returns {Promise<void>}
	 */
	async saveChatHistory(chatId) {
		try {
			const history = this.chatHistories.get(chatId) || []
			await DataManager.save(`data/chats/${chatId}.json`, history)
		} catch (error) {
			console.error(`Failed to save chat history for ${chatId}:`, error)
		}
	}

	/**
	 * Add message to chat history
	 * @param {string} chatId - Chat identifier
	 * @param {ChatMessage} message - Message to add
	 * @returns {Promise<void>}
	 */
	async addMessage(chatId, message) {
		if (!this.initialized) {
			await this.initialize()
		}

		// Ensure message has required fields
		const normalizedMessage = {
			content: message.content || '',
			role: message.role || 'user',
			timestamp: message.timestamp || Date.now(),
			name: message.name || (message.role === 'user' ? 'User' : 'Assistant'),
			files: message.files || [],
			extension: message.extension || {}
		}

		// Get or create chat history
		let history = this.chatHistories.get(chatId) || []

		// Add message to history
		history.push(normalizedMessage)

		// Trim history if too long
		if (history.length > this.maxHistoryLength) {
			history = history.slice(-this.maxHistoryLength)
		}

		// Update in memory and save to storage
		this.chatHistories.set(chatId, history)
		await this.saveChatHistory(chatId)
	}

	/**
	 * Update existing message in chat history
	 * @param {string} chatId - Chat identifier
	 * @param {string} messageId - Message ID to update
	 * @param {ChatMessage} updatedMessage - Updated message
	 * @returns {Promise<boolean>} True if message was found and updated
	 */
	async updateMessage(chatId, messageId, updatedMessage) {
		if (!this.initialized) {
			await this.initialize()
		}

		const history = this.chatHistories.get(chatId) || []

		// Find message by ID
		const messageIndex = history.findIndex(msg => msg.id === messageId)
		if (messageIndex === -1) {
			return false
		}

		// Update message
		history[messageIndex] = {
			...history[messageIndex],
			...updatedMessage,
			timestamp: updatedMessage.timestamp || history[messageIndex].timestamp
		}

		// Save updated history
		this.chatHistories.set(chatId, history)
		await this.saveChatHistory(chatId)
		return true
	}

	/**
	 * Get chat history
	 * @param {string} chatId - Chat identifier
	 * @param {number} [limit] - Maximum number of messages to return
	 * @returns {Promise<ChatMessage[]>} Chat history
	 */
	async getHistory(chatId, limit = null) {
		if (!this.initialized) {
			await this.initialize()
		}

		const history = this.chatHistories.get(chatId) || []
		
		if (limit && limit > 0) {
			return history.slice(-limit)
		}
		
		return [...history]
	}

	/**
	 * Clear chat history
	 * @param {string} chatId - Chat identifier
	 * @returns {Promise<void>}
	 */
	async clearHistory(chatId) {
		this.chatHistories.set(chatId, [])
		await this.saveChatHistory(chatId)
	}

	/**
	 * Delete chat history
	 * @param {string} chatId - Chat identifier
	 * @returns {Promise<void>}
	 */
	async deleteHistory(chatId) {
		this.chatHistories.delete(chatId)
		try {
			await DataManager.delete(`data/chats/${chatId}.json`)
		} catch (error) {
			console.warn(`Failed to delete chat history file for ${chatId}:`, error)
		}
	}

	/**
	 * Get all chat IDs
	 * @returns {string[]} Array of chat IDs
	 */
	getAllChatIds() {
		return Array.from(this.chatHistories.keys())
	}

	/**
	 * Get chat statistics
	 * @param {string} chatId - Chat identifier
	 * @returns {Object} Chat statistics
	 */
	getChatStats(chatId) {
		const history = this.chatHistories.get(chatId) || []
		
		const userMessages = history.filter(msg => msg.role === 'user').length
		const assistantMessages = history.filter(msg => msg.role === 'assistant').length
		const systemMessages = history.filter(msg => msg.role === 'system').length
		
		const firstMessage = history[0]
		const lastMessage = history[history.length - 1]
		
		return {
			totalMessages: history.length,
			userMessages,
			assistantMessages,
			systemMessages,
			firstMessageTime: firstMessage?.timestamp || null,
			lastMessageTime: lastMessage?.timestamp || null
		}
	}

	/**
	 * Search messages in chat history
	 * @param {string} chatId - Chat identifier
	 * @param {string} query - Search query
	 * @param {number} [limit] - Maximum number of results
	 * @returns {ChatMessage[]} Matching messages
	 */
	searchMessages(chatId, query, limit = 10) {
		const history = this.chatHistories.get(chatId) || []
		const lowerQuery = query.toLowerCase()
		
		const matches = history.filter(message => 
			message.content.toLowerCase().includes(lowerQuery) ||
			message.name.toLowerCase().includes(lowerQuery)
		)
		
		return matches.slice(-limit)
	}

	/**
	 * Convert legacy chat log format to new format
	 * @param {any[]} legacyChatLog - Legacy chat log
	 * @returns {ChatMessage[]} Converted chat messages
	 */
	convertLegacyChatLog(legacyChatLog) {
		if (!Array.isArray(legacyChatLog)) {
			return []
		}

		return legacyChatLog.map(entry => {
			// Handle different legacy formats
			if (typeof entry === 'string') {
				return {
					content: entry,
					role: 'user',
					timestamp: Date.now(),
					name: 'User',
					files: [],
					extension: {}
				}
			}

			// Handle object format
			return {
				content: entry.content || entry.text || '',
				role: entry.role || (entry.name === 'system' ? 'system' : 
					   entry.name?.includes('龙胆') || entry.name?.includes('Gentian') ? 'assistant' : 'user'),
				timestamp: entry.timestamp || entry.timeStamp || Date.now(),
				name: entry.name || (entry.role === 'user' ? 'User' : 'Assistant'),
				files: entry.files || [],
				extension: entry.extension || {}
			}
		})
	}

	/**
	 * Create chat request object for legacy compatibility
	 * @param {string} chatId - Chat identifier
	 * @param {string} chatName - Chat display name
	 * @param {string} username - User name
	 * @param {ChatMessage[]} chatLog - Chat history
	 * @param {Object} [options] - Additional options
	 * @returns {ChatRequest} Chat request object
	 */
	createChatRequest(chatId, chatName, username, chatLog, options = {}) {
		const config = configManager.get()
		
		return {
			chatId,
			chatName,
			username,
			characterName: config?.character?.info?.name || '龙胆•阿芙萝黛蒂',
			locales: [config?.character?.info?.defaultLocale || 'zh-CN'],
			timestamp: Date.now(),
			chatLog: this.convertLegacyChatLog(chatLog),
			memory: options.memory || {},
			extension: options.extension || {}
		}
	}

	/**
	 * Cleanup old chat histories
	 * @param {number} maxAge - Maximum age in milliseconds
	 * @returns {Promise<number>} Number of cleaned up chats
	 */
	async cleanup(maxAge = 30 * 24 * 60 * 60 * 1000) { // 30 days default
		let cleanedCount = 0
		const now = Date.now()

		for (const [chatId, history] of this.chatHistories) {
			if (history.length === 0) {
				await this.deleteHistory(chatId)
				cleanedCount++
				continue
			}

			const lastMessage = history[history.length - 1]
			if (lastMessage && (now - lastMessage.timestamp) > maxAge) {
				await this.deleteHistory(chatId)
				cleanedCount++
			}
		}

		console.log(`Cleaned up ${cleanedCount} old chat histories`)
		return cleanedCount
	}

	/**
	 * Get memory usage statistics
	 * @returns {Object} Memory usage statistics
	 */
	getMemoryStats() {
		let totalMessages = 0
		let totalChats = this.chatHistories.size

		for (const history of this.chatHistories.values()) {
			totalMessages += history.length
		}

		return {
			totalChats,
			totalMessages,
			averageMessagesPerChat: totalChats > 0 ? Math.round(totalMessages / totalChats) : 0,
			maxHistoryLength: this.maxHistoryLength
		}
	}
}

// Export singleton instance
export const chatManager = new ChatManager()

// Export class for testing
export { ChatManager as ChatManagerClass }
