/**
 * Fount platform compatibility layer
 * Provides compatibility functions for legacy code that depends on fount platform
 */

import { configManager } from '../core/config.mjs'
import { DataManager } from '../core/data-manager.mjs'

/**
 * Mock localhostLocales for compatibility
 * @type {string[]}
 */
export const localhostLocales = ['zh-CN', 'en-US']

/**
 * Mock getPartInfo function for compatibility
 * @param {any} part - Part object (can be anything)
 * @param {string[]} [locales] - Locale preferences
 * @returns {Promise<Object>} Part information
 */
export async function getPartInfo(part, locales = localhostLocales) {
	const config = configManager.get()
	
	// If part is a string (AI source name), return AI source info
	if (typeof part === 'string') {
		return {
			name: part,
			provider: 'Independent AI',
			version: config?.version || '2.0.0',
			description: `AI Source: ${part}`
		}
	}
	
	// If part is the character object, return character info
	if (part && typeof part === 'object') {
		return {
			name: config?.character?.info?.name || '龙胆•阿芙萝黛蒂',
			provider: 'Independent Character',
			version: config?.version || '2.0.0',
			description: config?.character?.info?.description || 'AI Character Companion'
		}
	}
	
	// Default fallback
	return {
		name: 'Unknown',
		provider: 'Independent',
		version: config?.version || '2.0.0',
		description: 'Unknown component'
	}
}

/**
 * Mock reloadPart function for compatibility
 * @param {any} part - Part to reload
 * @returns {Promise<void>}
 */
export async function reloadPart(part) {
	console.log('reloadPart called (compatibility mode) - no action taken')
}

/**
 * Mock loadJsonFileIfExists function for compatibility
 * @param {string} filepath - File path
 * @param {any} defaultValue - Default value if file doesn't exist
 * @returns {Promise<any>} Loaded data
 */
export async function loadJsonFileIfExists(filepath, defaultValue = null) {
	return await DataManager.load(filepath, defaultValue)
}

/**
 * Mock saveJsonFile function for compatibility
 * @param {string} filepath - File path
 * @param {any} data - Data to save
 * @returns {Promise<void>}
 */
export async function saveJsonFile(filepath, data) {
	await DataManager.save(filepath, data)
}

/**
 * Mock buildPromptStruct function for compatibility
 * @param {Object} request - Chat request
 * @returns {Promise<Object>} Prompt structure
 */
export async function buildPromptStruct(request) {
	// Simplified prompt structure for compatibility
	return {
		system: request.system || '',
		user: request.user || '',
		assistant: request.assistant || '',
		context: request.context || '',
		memory: request.memory || {},
		functions: request.functions || {},
		metadata: {
			timestamp: Date.now(),
			version: '2.0.0'
		}
	}
}

/**
 * Mock notify function for compatibility
 * @param {string} message - Notification message
 * @param {Object} [options] - Notification options
 * @returns {Promise<void>}
 */
export async function notify(message, options = {}) {
	console.log(`[NOTIFICATION] ${message}`, options)
}

/**
 * Create compatibility exports for common fount modules
 */
export const fountCompat = {
	localhostLocales,
	getPartInfo,
	reloadPart,
	loadJsonFileIfExists,
	saveJsonFile,
	buildPromptStruct,
	notify
}

// Export individual functions for direct import compatibility
export default fountCompat
