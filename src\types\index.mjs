/**
 * Core type definitions for Gentian Aphrodite
 * Independent implementation replacing fount platform dependencies
 */

// =============================================================================
// Basic Types
// =============================================================================

/**
 * @typedef {number} TimeStamp
 */

/**
 * @typedef {string} Locale
 */

/**
 * @typedef {'user' | 'assistant' | 'system'} Role
 */

// =============================================================================
// Chat System Types
// =============================================================================

/**
 * Chat message structure
 * @typedef {Object} ChatMessage
 * @property {string} content - Message content
 * @property {Role} role - Message role
 * @property {TimeStamp} timestamp - Message timestamp
 * @property {string} [name] - Sender name
 * @property {string} [avatar] - Sender avatar URL
 * @property {ChatFile[]} [files] - Attached files
 * @property {Object} [extension] - Platform-specific extensions
 */

/**
 * Chat file attachment
 * @typedef {Object} ChatFile
 * @property {string} name - File name
 * @property {string} mimeType - MIME type
 * @property {Uint8Array} buffer - File data
 * @property {string} [description] - File description
 */

/**
 * Chat reply structure
 * @typedef {Object} ChatReply
 * @property {string} content - Reply content
 * @property {string} [name] - Sender name
 * @property {string} [avatar] - Sender avatar URL
 * @property {ChatFile[]} [files] - Attached files
 * @property {Object} [extension] - Platform-specific extensions
 */

/**
 * Chat request structure
 * @typedef {Object} ChatRequest
 * @property {string} chatId - Chat identifier
 * @property {string} chatName - Chat display name
 * @property {string} username - User name
 * @property {string} characterName - Character name
 * @property {Locale[]} locales - Supported locales
 * @property {TimeStamp} timestamp - Request timestamp
 * @property {ChatMessage[]} chatLog - Chat history
 * @property {Object} memory - Chat-scoped memory
 * @property {Object} [extension] - Platform-specific extensions
 */

// =============================================================================
// AI Source Types
// =============================================================================

/**
 * AI source configuration
 * @typedef {Object} AISourceConfig
 * @property {string} name - Source name
 * @property {string} type - Source type (openai, claude, custom)
 * @property {string} apiKey - API key
 * @property {string} baseUrl - Base URL
 * @property {string} model - Model name
 * @property {number} [maxTokens] - Maximum tokens
 * @property {number} [temperature] - Temperature setting
 * @property {number} [topP] - Top-p setting
 * @property {Object} [options] - Additional options
 */

/**
 * AI source interface
 * @typedef {Object} AISource
 * @property {string} name - Source name
 * @property {string} type - Source type
 * @property {(prompt: string, options?: Object) => Promise<string>} call - Call AI
 * @property {() => boolean} isAvailable - Check availability
 * @property {() => Object} getInfo - Get source info
 */

// =============================================================================
// Character Types
// =============================================================================

/**
 * Character information
 * @typedef {Object} CharacterInfo
 * @property {string} name - Character name
 * @property {string} description - Character description
 * @property {string} avatar - Avatar URL
 * @property {string} version - Character version
 * @property {string} author - Character author
 * @property {string[]} tags - Character tags
 * @property {Locale} defaultLocale - Default locale
 */

/**
 * Character configuration
 * @typedef {Object} CharacterConfig
 * @property {CharacterInfo} info - Character information
 * @property {Object} personality - Personality settings
 * @property {Object} memory - Memory configuration
 * @property {Object} behavior - Behavior settings
 * @property {Object} aiSources - AI source mappings
 */

// =============================================================================
// Platform Integration Types
// =============================================================================

/**
 * Platform interface
 * @typedef {Object} PlatformInterface
 * @property {string} name - Platform name
 * @property {(message: ChatMessage) => Promise<void>} sendMessage - Send message
 * @property {(channelId: string) => Promise<void>} sendTyping - Send typing indicator
 * @property {(channelId: string, limit: number) => Promise<ChatMessage[]>} getHistory - Get chat history
 * @property {() => string} getBotId - Get bot ID
 * @property {() => string} getBotName - Get bot name
 * @property {() => Promise<void>} destroy - Cleanup platform
 */

/**
 * Discord-specific types
 * @typedef {Object} DiscordConfig
 * @property {string} token - Bot token
 * @property {string} clientId - Client ID
 * @property {string} [guildId] - Guild ID
 * @property {Object} [options] - Additional options
 */

/**
 * Telegram-specific types
 * @typedef {Object} TelegramConfig
 * @property {string} token - Bot token
 * @property {string} [webhookUrl] - Webhook URL
 * @property {Object} [options] - Additional options
 */

// =============================================================================
// Memory Types
// =============================================================================

/**
 * Memory entry
 * @typedef {Object} MemoryEntry
 * @property {string} id - Entry ID
 * @property {string} content - Memory content
 * @property {TimeStamp} timestamp - Creation timestamp
 * @property {number} relevance - Relevance score
 * @property {string[]} keywords - Associated keywords
 * @property {string} chatName - Associated chat
 * @property {Object} [metadata] - Additional metadata
 */

/**
 * Memory manager interface
 * @typedef {Object} MemoryManager
 * @property {(entry: MemoryEntry) => Promise<void>} add - Add memory entry
 * @property {(query: string, limit?: number) => Promise<MemoryEntry[]>} search - Search memories
 * @property {(id: string) => Promise<void>} remove - Remove memory entry
 * @property {() => Promise<void>} cleanup - Cleanup old memories
 * @property {() => Promise<MemoryEntry[]>} getAll - Get all memories
 */

// =============================================================================
// Configuration Types
// =============================================================================

/**
 * Application configuration
 * @typedef {Object} AppConfig
 * @property {string} name - Application name
 * @property {string} version - Application version
 * @property {string} environment - Environment (dev/prod)
 * @property {string} logLevel - Log level
 * @property {Object} server - Server configuration
 * @property {Object} storage - Storage configuration
 * @property {CharacterConfig} character - Character configuration
 * @property {Object} platforms - Platform configurations
 * @property {Object} aiSources - AI source configurations
 */

// =============================================================================
// Export all types for JSDoc usage
// =============================================================================

export {
	// This file is used for type definitions only
	// All types are defined as JSDoc comments for use in .mjs files
}
